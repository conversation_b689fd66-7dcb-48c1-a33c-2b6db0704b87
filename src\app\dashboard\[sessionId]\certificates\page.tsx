import PageWrapper from "../_components/layout/PageWrapper";
import CustomDialog from "@/components/shared/CustomDialog";
import { Button } from "@/components/ui/button";
import { Suspense } from "react";
import Loading from "../_components/Loading";
import AddCertModal from "./_components/add-cert-modal";
import { getTemplates } from "@/lib/server/action/certificates/templates";
import Image from "next/image";
import DeleteTemplate from "./_components/delete-template";

const breadcrumbItems = [
  { label: "Home", href: "/dashboard" },
  { label: "Certificates" },
];

async function SuspendedComponent({
  params,
}: {
  params: Promise<{ sessionId: string }>;
}) {
  const { sessionId } = await params;
  const { templates, total } = await getTemplates(sessionId);
  if (templates.length === 0) {
    return <div>No templates found</div>;
  }

  return (
    <div className="flex flex-col bg-muted p-4 rounded-lg">
      <p className="ml-auto text-sm font-medium px-4">Total: {total}</p>
      <ul className="flex flex-wrap justify-center gap-6 py-6 px-2">
        {templates.map((template, index) => (
          <li key={template.id}>
            <div className="relative border-2 border-dashed border-gray-300 rounded-lg p-2 text-center space-y-2">
              <div className="absolute top-2 right-2 z-20">
                <DeleteTemplate templateId={template.id} />
              </div>
              <div className="relative w-80 h-72 overflow-hidden rounded-lg">
                <Image
                  src={"/images/placeholder.svg"}
                  alt="certificate template"
                  fill
                  className="object-cover"
                />
              </div>
              <p className="text-sm text-gray-600">Template {index + 1}</p>
            </div>
          </li>
        ))}
      </ul>
    </div>
  );
}

export default function CertificatesPage({
  params,
}: {
  params: Promise<{ sessionId: string }>;
}) {
  const RenderButton = () => (
    <CustomDialog
      title="Add Certificate"
      trigger={<Button size="sm">Add Certificate</Button>}
    >
      <AddCertModal />
    </CustomDialog>
  );

  return (
    <PageWrapper
      pgTitle="Manage Certificate"
      breadcrumbItems={breadcrumbItems}
      headerButton={<RenderButton />}
    >
      <Suspense fallback={<Loading />}>
        <SuspendedComponent params={params} />
      </Suspense>
    </PageWrapper>
  );
}
