import { auth } from "../../../auth";
import Footer from "./_components/layout/footer";
import Header from "./_components/layout/header";

export default async function HomeLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const session = await auth();

  return (
    <div className="flex flex-col min-h-dvh">
      <Header user={session?.user} />
      <div className="flex-1">{children}</div>
      <Footer />
    </div>
  );
}
