import { Button } from "@/components/ui/button";
import { Suspense } from "react";
import { DataTable } from "../_components/table/data-table";
import PageWrapper from "../_components/layout/PageWrapper";
import Loading from "../_components/Loading";
import { getGrades } from "@/lib/server/action/grades";
import { columns } from "./columns";
import { CustomSheet } from "@/components/shared/CustomSheet";
import GradeForm from "@/components/form/GradeForm";

const breadcrumbItems = [
  { label: "Home", href: "/dashboard" },
  { label: "Grade" },
];

// const pageAssess = ["super-grade"];

async function SuspendedComponent({
  params,
}: {
  params: Promise<{ sessionId: string }>;
}) {
  const { sessionId } = await params;
  const { grades } = await getGrades({
    sessionId,
  });

  return (
    <>
      <DataTable columns={columns} data={grades} />
    </>
  );
}

const AllGradesPAge = ({
  params,
}: {
  params: Promise<{ sessionId: string }>;
}) => {
  const RenderButton = () => (
    <CustomSheet
      title="Create Grade"
      trigger={<Button size="sm">Add Grade</Button>}
    >
      <GradeForm />
    </CustomSheet>
  );

  return (
    <PageWrapper
      pgTitle="Manage Grades"
      breadcrumbItems={breadcrumbItems}
      headerButton={<RenderButton />}
    >
      <Suspense fallback={<Loading />}>
        <SuspendedComponent params={params} />
      </Suspense>
    </PageWrapper>
  );
};

export default AllGradesPAge;
