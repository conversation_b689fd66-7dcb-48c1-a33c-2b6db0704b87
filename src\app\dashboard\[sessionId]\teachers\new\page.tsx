import { But<PERSON> } from "@/components/ui/button";
import { ChevronLeft } from "lucide-react";
import TeacherForm from "@/components/form/TeacherForm";
import Link from "next/link";

// const pageAssess = ["super-admin"];

export default async function CreateTeacherPage({
  params,
}: {
  params: Promise<{ sessionId: string }>;
}) {
  const { sessionId } = await params;
  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2 -mt-2">
        <Button variant="outline" size="sm" asChild>
          <Link href={`/dashboard/${sessionId}/teachers`}>
            <ChevronLeft className="h-4 w-4" />
            Back to Teachers
          </Link>
        </Button>
      </div>
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Create Teacher</h1>
      </div>
      <div className="rounded-lg border bg-card p-6">
        <TeacherForm sessionId={sessionId} isAdmin={true} />
      </div>
    </div>
  );
}
