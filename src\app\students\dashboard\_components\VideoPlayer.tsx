"use client";

import { useState, useRef, useEffect } from "react";

interface VideoPlayerProps {
  resource: {
    id: string;
    name: string;
    url: string;
  };
  onProgress: (progress: number) => void;
}

export default function VideoPlayer({
  resource,
  onProgress,
}: VideoPlayerProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [watchTime, setWatchTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [hasStarted, setHasStarted] = useState(false);

  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const handleTimeUpdate = () => {
      const currentTime = video.currentTime;
      const totalDuration = video.duration;

      if (totalDuration > 0) {
        const progress = (currentTime / totalDuration) * 100;
        setWatchTime(currentTime);
        setDuration(totalDuration);

        // Report progress every 10% watched
        if (
          progress > 0 &&
          Math.floor(progress / 10) >
            Math.floor((watchTime / totalDuration) * 10)
        ) {
          onProgress(Math.min(progress, 100));
        }
      }
    };

    const handlePlay = () => {
      if (!hasStarted) {
        setHasStarted(true);
        onProgress(5); // Give 5% for starting the video
      }
    };

    const handleEnded = () => {
      onProgress(100); // Full completion
    };

    video.addEventListener("timeupdate", handleTimeUpdate);
    video.addEventListener("play", handlePlay);
    video.addEventListener("ended", handleEnded);

    return () => {
      video.removeEventListener("timeupdate", handleTimeUpdate);
      video.removeEventListener("play", handlePlay);
      video.removeEventListener("ended", handleEnded);
    };
  }, [onProgress, watchTime, duration, hasStarted]);

  const progressPercent = duration > 0 ? (watchTime / duration) * 100 : 0;

  return (
    <div className="bg-gray-50 rounded-lg p-4">
      <h5 className="font-medium mb-3">{resource.name}</h5>

      <div className="relative">
        <video
          ref={videoRef}
          className="w-full rounded-lg"
          controls
          preload="metadata"
        >
          <source src={resource.url} type="video/mp4" />
          Your browser does not support the video tag.
        </video>

        <div className="mt-2">
          <div className="flex justify-between text-sm text-gray-600 mb-1">
            <span>Progress</span>
            <span>{Math.round(progressPercent)}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-green-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progressPercent}%` }}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
