'use server'

import prisma from "@/lib/prisma"
import { revalidatePath } from "next/cache"
import { TSessionForm } from "./session.schema"

export async function createSession (data: TSessionForm) {
  try {
    const existingSession = await prisma.session.findUnique({
      where: { name: data.name }
    })
    
    if (existingSession) {
      return { success: false, error: 'Session with this name already exists' }
    }

    const isActive = data.isActive === "true" ? true : false

    if (isActive) {
      const existingActiveSession = await prisma.session.findFirst({
        where: { isActive: true }
      })
      
      if (existingActiveSession) {
        await prisma.session.update({
          where: { id: existingActiveSession.id },
          data: {
            isActive: false,
          },
        })
      }
    }

    await prisma.session.create({
      data: {
        name: data.name,
        description: data.description,
        startDate: data.startDate,
        endDate: data.endDate,
        isActive,
      },
    })
    
    revalidatePath('/dashboard')

    return { success: true, message: 'Session created' }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to create session' }
  }
}

export async function updateSession (sessionId: string, data: TSessionForm) {
  try {
    const existingSession = await prisma.session.findUnique({
      where: { name: data.name }
    })
    
    if (existingSession && existingSession.id !== sessionId) {
      return { success: false, error: 'Session with this name already exists' }
    }

    await prisma.session.update({
      where: { id: sessionId },
      data: {
        name: data.name,
        description: data.description,
        startDate: data.startDate,
        endDate: data.endDate,
      },
    })
    
    revalidatePath('/dashboard')

    return { success: true, message: 'Session updated' }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to update session' }
  }
}

export async function activateSession (sessionId: string) {
  try {
    // Check if there is already an active session
    const activeSession = await prisma.session.findFirst({
      where: { isActive: true }
    })
    
    if (activeSession) {
      await prisma.session.update({
        where: { id: activeSession.id },
        data: {
          isActive: false,
        },
      })
    }

    await prisma.session.update({
      where: { id: sessionId },
      data: {
        isActive: true,
      },
    })
    
    revalidatePath('/dashboard')

    return { success: true, message: 'Session activated' }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to activate session' }
  }
}

export async function deactivateSession (sessionId: string) {
  try {
    await prisma.session.update({
      where: { id: sessionId },
      data: {
        isActive: false,
      },
    })
    
    revalidatePath('/dashboard')

    return { success: true, message: 'Session deactivated' }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to deactivate session' }
  }
}

export async function deleteSession (sessionId: string) {
  try {
    await prisma.session.delete({
      where: { id: sessionId }
    })
    
    revalidatePath('/dashboard')

    return { success: true }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to delete session' }
  }
}