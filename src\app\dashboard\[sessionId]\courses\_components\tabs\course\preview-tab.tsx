import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import Image from "next/image";

export default async function CoursePreviewTab({
  title,
  description,
  grade,
  primaryTeacherName,
}: {
  title: string;
  description?: string;
  grade: string;
  primaryTeacherName?: string;
}) {
  return (
    <Card className="overflow-hidden py-0 gap-0">
      <div className="relative h-48">
        <Image
          src={"/images/placeholder.svg"}
          alt={title}
          fill
          className="object-cover"
        />
      </div>
      <CardContent className="p-6">
        <div className="space-y-4">
          <div>
            <h2 className="text-2xl font-bold">{title}</h2>
            <p className="text-gray-600 mt-2">
              {description || "Course description will appear here"}
            </p>
          </div>

          <div className="flex items-center gap-2">
            {grade && <Badge variant="outline">{grade}</Badge>}
          </div>

          <div className="flex items-center justify-between pt-4 border-t">
            <div className="flex items-center gap-2">
              <Avatar className="w-8 h-8">
                <AvatarFallback>
                  {primaryTeacherName
                    ? primaryTeacherName
                        .split(" ")
                        .map((n) => n[0])
                        .join("")
                    : "IN"}
                </AvatarFallback>
              </Avatar>
              <span className="font-medium">
                {primaryTeacherName || "Instructor Name"}
              </span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
