'use server'

import prisma from "@/lib/prisma"
import { auth } from "@/lib/server/auth";

export async function getMeetings(courseId: string) {
  const session = await auth()

  if (!session) {
    throw new Error("Unauthorized")
  }

  try {
    const meetings = await prisma.meeting.findMany({
      where: {
        OR: [
          { hostId: courseId },
          {
            participants: {
              some: { userId: session.user.id }
            }
          }
        ]
      },
      include: {
        host: {
          select: { title: true }
        },
        participants: {
          include: { user: { select: { firstName: true, lastName: true, email: true } } }
        }
      },
      orderBy: { scheduledAt: 'asc' }
    })

    return meetings;
  } catch (error) {
    console.log(error)
    throw new Error("Failed to fetch meetings.")
  }
}

export type MeetingWithHostAndParticipants = Awaited<ReturnType<typeof getMeetings>>[number];