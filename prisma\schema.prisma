generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  // provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Enums
enum UserRole {
  ADMIN
  TEACHER
  STUDENT
}

enum UserStatus {
  PENDING
  APPROVED
  REJECTED
  SUSPENDED
}

enum CodeStatus {
  INACTIVE
  ACTIVE
  EXPIRED
  REVOKED
}

enum ResourceType {
  VIDEO
  PDF
  IMAGE
  PPT
  LINK
  ZOOM_LINK
  GMEET_LINK
}

enum AttemptStatus {
  IN_PROGRESS
  COMPLETED
  ABANDONED
}

enum PublishStatus {
  PUBLISHED
  DRAFT
  ARCHIVED
}

enum TeacherRole {
  PRIMARY
  ASSISTANT
}

enum LoginCodeType {
  TEACHER
  STUDENT
}

enum LessonType {
  VIDEO
  DOC
}

enum ActivityStatus {
  SUBMITTED
  GRADED
  EXPIRED
}

enum MeetingType {
  CONFERENCE_CALL
  VIDEO_CALL
}

enum MeetingStatus {
  SCHEDULED
  ONGOING
  COMPLETED
  CANCELLED
}

model Session {
  id          String   @id @default(cuid())
  name        String   @unique
  description String?
  startDate   DateTime
  endDate     DateTime
  isActive    Boolean  @default(false)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  grade               Grade[]
  school              School[]
  student             StudentProfile[]
  course              Course[]
  teacher             TeacherProfile[]
  certificateTemplate CertificateTemplate[]
  code                Code[]

  @@map("sessions")
}

model User {
  id        String     @id @default(cuid())
  email     String     @unique
  firstName String
  lastName  String
  role      UserRole
  status    UserStatus @default(PENDING)

  phone  String?
  avatar String?

  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  lastLogin DateTime?

  code           Code?
  adminProfile   AdminProfile?
  teacherProfile TeacherProfile?
  studentProfile StudentProfile?
  loginAttempts  LoginAttempt[]

  participantMeetings MeetingParticipant[]

  @@map("users")
}

model AdminProfile {
  id       String   @id @default(cuid())
  password String
  isFirst  Boolean? @default(false)

  userId String @unique
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  permissions Json?

  @@map("admin_profiles")
}

model TeacherProfile {
  id        String  @id @default(cuid())
  loginCode String? @unique
  userId    String  @unique
  user      User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  sessionId      String
  session        Session @relation(fields: [sessionId], references: [id])
  schoolId       String
  school         School  @relation(fields: [schoolId], references: [id])
  specialization String?
  bio            String?

  courseAssignments TeacherCourseAssignment[]

  @@map("teacher_profiles")
}

model StudentProfile {
  id            String    @id @default(cuid())
  userId        String    @unique
  user          User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  loginCode     String?   @unique
  courseId      String
  dateOfBirth   DateTime?
  guardianName  String?
  guardianEmail String?
  guardianPhone String?

  sessionId String
  session   Session @relation(fields: [sessionId], references: [id])
  studentId String  @unique
  schoolId  String
  school    School  @relation(fields: [schoolId], references: [id])
  gradeId   String
  grade     Grade   @relation(fields: [gradeId], references: [id])

  enrollment          Enrollment?
  assessmentAttempts  AssessmentAttempt[]
  progressTracking    StudentProgress[]
  certificates        StudentCertificate[]
  activityResponses   ActivityResponse[]
  moduleProgress      StudentModuleProgress[]
  lessonVideoProgress LessonVideoProgress[]
  // results            StudentResult[]

  @@map("student_profiles")
}

model Code {
  id         String        @id @default(cuid())
  type       LoginCodeType
  code       String        @unique
  expireTime DateTime?
  status     CodeStatus    @default(ACTIVE)

  userId    String  @unique
  user      User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  sessionId String
  session   Session @relation(fields: [sessionId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("codes")
}

model School {
  id          String  @id @default(cuid())
  name        String  @unique
  description String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  sessionId String
  session   Session @relation(fields: [sessionId], references: [id])

  students StudentProfile[]
  teachers TeacherProfile[]

  @@map("schools")
}

model GeneralSettings {
  id               String  @id @default(cuid())
  siteTitle        String
  siteName         String
  siteAddress      String
  email            String
  phone1           String?
  phone2           String?
  landline1        String?
  landline2        String?
  address          String?
  socials          Json?
  iconId           String?
  iconUrl          String?
  logoId           String?
  logoUrl          String?
  videoId          String?
  videoUrl         String?
  userIntoVideoId  String?
  userIntoVideoUrl String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("general_settings")
}

model Grade {
  id          String  @id @default(cuid())
  name        String
  description String?
  order       Int     @default(0)
  isActive    Boolean @default(true)

  sessionId String
  session   Session @relation(fields: [sessionId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  courses  Course[]
  students StudentProfile[]

  @@map("grades")
}

model Course {
  id          String        @id @default(cuid())
  title       String
  slug        String        @unique
  description String?
  thumbnail   String?
  isActive    Boolean       @default(true)
  order       Int           @default(0)
  status      PublishStatus @default(DRAFT)

  sessionId             String
  session               Session                    @relation(fields: [sessionId], references: [id])
  gradeId               String
  grade                 Grade                      @relation(fields: [gradeId], references: [id], onDelete: Cascade)
  certificateTemplateId String?
  certificateTemplate   AssignCertificateToCourse?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  activities         Activity[]
  modules            Module[]
  enrollments        Enrollment[]
  teacherAssignments TeacherCourseAssignment[]
  resources          CourseResource[]
  folders            CourseResourceFolder[]
  progress           StudentProgress[]
  hostedMeetings     Meeting[]                 @relation("MeetingHost")

  @@map("courses")
}

model TeacherCourseAssignment {
  id        String         @id @default(cuid())
  teacherId String
  teacher   TeacherProfile @relation(fields: [teacherId], references: [id], onDelete: Cascade)
  courseId  String
  course    Course         @relation(fields: [courseId], references: [id], onDelete: Cascade)
  role      TeacherRole    @default(PRIMARY)

  assignedAt DateTime @default(now())
  isActive   Boolean  @default(true)

  @@unique([teacherId, courseId])
  @@map("teacher_course_assignments")
}

model Activity {
  id       String   @id @default(cuid())
  question String
  points   Int      @default(100)
  isActive Boolean  @default(true)
  deadline DateTime

  courseId String
  course   Course @relation(fields: [courseId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  responses ActivityResponse[]

  @@map("activities")
}

model ActivityResponse {
  id          String         @id @default(cuid())
  response    String
  score       Int?
  status      ActivityStatus @default(SUBMITTED)
  submittedAt DateTime       @default(now())

  studentId  String
  student    StudentProfile @relation(fields: [studentId], references: [id], onDelete: Cascade)
  activityId String
  activity   Activity       @relation(fields: [activityId], references: [id], onDelete: Cascade)

  @@unique([studentId, activityId])
  @@map("activity_responses")
}

model Module {
  id          String  @id @default(cuid())
  title       String
  description String?
  order       Int     @default(0)
  isActive    Boolean @default(true)

  courseId String
  course   Course @relation(fields: [courseId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  resources  ModuleResource[]
  assessment Assessment?
  lesson     Lesson?
  progress   StudentModuleProgress[]

  @@unique([courseId, order])
  @@map("modules")
}

model CourseResourceFolder {
  id    String @id @default(cuid())
  name  String
  order Int    @default(0)

  courseId String
  course   Course @relation(fields: [courseId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  resources CourseResource[]

  @@map("course_resource_folders")
}

model CourseResource {
  id       String       @id @default(cuid())
  name     String
  type     ResourceType
  url      String?
  content  String?
  fileSize Int?
  mimeType String?
  order    Int?

  folderId String?
  folder   CourseResourceFolder? @relation(fields: [folderId], references: [id], onDelete: SetNull)
  courseId String
  course   Course                @relation(fields: [courseId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("course_resources")
}

model ModuleResource {
  id       String       @id @default(cuid())
  name     String
  type     ResourceType
  url      String?
  content  String?
  fileSize Int?
  mimeType String?
  order    Int?

  moduleId String
  module   Module @relation(fields: [moduleId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("module_resources")
}

// Lesson System
model Lesson {
  id          String     @id @default(cuid())
  title       String
  type        LessonType
  description String?
  docUrl      String?
  videoUrl    String?
  duration    String?

  moduleId String @unique
  module   Module @relation(fields: [moduleId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  progress LessonVideoProgress[]

  @@map("lessons")
}

// Assessment System
model Assessment {
  id                  String  @id @default(cuid())
  instructions        String?
  maxAttempts         Int     @default(3)
  maxStudentQuestions Int     @default(20)
  passingScore        Float   @default(70.0)
  isActive            Boolean @default(true)

  moduleId String @unique
  module   Module @relation(fields: [moduleId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  questions Question[]
  attempts  AssessmentAttempt[]

  @@map("assessments")
}

model Question {
  id           String @id @default(cuid())
  question     String
  questionType String @default("multiple_choice") // multiple_choice, true_false
  points       Float  @default(1.0)
  order        Int    @default(0)
  questionHash String

  assessmentId String
  assessment   Assessment @relation(fields: [assessmentId], references: [id], onDelete: Cascade)

  options       String?
  correctAnswer String

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  responses QuestionResponse[]

  @@unique([questionHash, assessmentId])
  @@map("questions")
}

model AssessmentAttempt {
  id            String        @id @default(cuid())
  attemptNumber Int
  status        AttemptStatus @default(IN_PROGRESS)
  startedAt     DateTime      @default(now())
  completedAt   DateTime?
  score         Float?
  totalPoints   Float?
  passed        Boolean?
  timeSpent     Int?

  studentId    String
  student      StudentProfile @relation(fields: [studentId], references: [id], onDelete: Cascade)
  assessmentId String
  assessment   Assessment     @relation(fields: [assessmentId], references: [id], onDelete: Cascade)

  // Relationships
  responses QuestionResponse[]

  @@unique([studentId, assessmentId, attemptNumber])
  @@map("assessment_attempts")
}

model QuestionResponse {
  id           String   @id @default(cuid())
  response     String
  isCorrect    Boolean?
  pointsEarned Float?

  attemptId  String
  attempt    AssessmentAttempt @relation(fields: [attemptId], references: [id], onDelete: Cascade)
  questionId String
  question   Question          @relation(fields: [questionId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())

  @@unique([attemptId, questionId])
  @@map("question_responses")
}

// Student Progress Tracking
model Enrollment {
  id          String    @id @default(cuid())
  enrolledAt  DateTime  @default(now())
  completedAt DateTime?
  isActive    Boolean   @default(true)
  progress    Float     @default(0.0) // Percentage completion

  studentId String         @unique
  student   StudentProfile @relation(fields: [studentId], references: [id], onDelete: Cascade)
  courseId  String
  course    Course         @relation(fields: [courseId], references: [id], onUpdate: Cascade, onDelete: Cascade)

  @@unique([studentId, courseId])
  @@map("enrollments")
}

model StudentProgress {
  id           String    @id @default(cuid())
  progress     Float     @default(0.0) // Percentage completion
  completedAt  DateTime?
  lastAccessed DateTime  @default(now())

  studentId String
  student   StudentProfile @relation(fields: [studentId], references: [id], onDelete: Cascade)
  courseId  String
  course    Course         @relation(fields: [courseId], references: [id], onDelete: Cascade)

  updatedAt DateTime @updatedAt

  @@unique([studentId, courseId])
  @@map("student_progress")
}

model StudentModuleProgress {
  id           String    @id @default(cuid())
  progress     Float?    @default(0.0)
  isCompleted  Boolean   @default(false)
  completedAt  DateTime?
  lastAccessed DateTime  @default(now())

  studentId String
  student   StudentProfile @relation(fields: [studentId], references: [id], onDelete: Cascade)
  moduleId  String
  module    Module         @relation(fields: [moduleId], references: [id], onDelete: Cascade)

  updatedAt DateTime @updatedAt

  @@unique([studentId, moduleId])
  @@map("student_module_progress")
}

model LessonVideoProgress {
  id            String    @id @default(cuid())
  videoDuration String?
  progress      Float     @default(0.0)
  hasWatched    Boolean   @default(false)
  isCompleted   Boolean   @default(false)
  completedAt   DateTime?
  lastAccessed  DateTime  @default(now())

  studentId String
  student   StudentProfile @relation(fields: [studentId], references: [id], onDelete: Cascade)
  lessonId  String
  lesson    Lesson         @relation(fields: [lessonId], references: [id], onDelete: Cascade)

  updatedAt DateTime @updatedAt

  @@unique([studentId, lessonId])
  @@map("lesson_video_progress")
}

// Certificate Management
model CertificateTemplate {
  id          String  @id @default(cuid())
  name        String
  templateUrl String
  isActive    Boolean @default(true)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  sessionId String
  session   Session @relation(fields: [sessionId], references: [id])

  // Relationships
  certificates StudentCertificate[]
  courses      AssignCertificateToCourse[]

  @@map("certificate_templates")
}

model AssignCertificateToCourse {
  id         String              @id @default(cuid())
  courseId   String              @unique
  course     Course              @relation(fields: [courseId], references: [id], onDelete: Cascade)
  templateId String
  template   CertificateTemplate @relation(fields: [templateId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([courseId, templateId])
  @@map("assign_certificate_to_course")
}

model StudentCertificate {
  id             String   @id @default(cuid())
  certificateUrl String
  issuedAt       DateTime @default(now())
  isValid        Boolean  @default(true)

  studentId  String
  student    StudentProfile      @relation(fields: [studentId], references: [id], onDelete: Cascade)
  templateId String
  template   CertificateTemplate @relation(fields: [templateId], references: [id])

  // Certificate metadata
  metadata Json? // Additional certificate data

  @@map("student_certificates")
}

model LoginAttempt {
  id            String   @id @default(cuid())
  email         String?
  loginCode     String?
  ipAddress     String?
  userAgent     String?
  successful    Boolean
  failureReason String?
  attemptedAt   DateTime @default(now())

  userId String?
  user   User?   @relation(fields: [userId], references: [id])

  @@map("login_attempts")
}

model Meeting {
  id          String        @id @default(cuid())
  title       String
  description String?
  type        MeetingType   @default(CONFERENCE_CALL)
  status      MeetingStatus @default(SCHEDULED)

  // Google Meet integration
  meetUrl String?
  meetId  String?

  scheduledAt DateTime
  startedAt   DateTime?
  endedAt     DateTime?

  hostId String
  host   Course @relation("MeetingHost", fields: [hostId], references: [id])

  participants MeetingParticipant[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model MeetingParticipant {
  id        String    @id @default(cuid())
  userId    String
  meetingId String
  joinedAt  DateTime?
  leftAt    DateTime?

  user    User    @relation(fields: [userId], references: [id])
  meeting Meeting @relation(fields: [meetingId], references: [id])

  @@unique([userId, meetingId])
}
