'use server'

import prisma from "@/lib/prisma"
import { Prisma } from "@prisma/client";

export type CourseResourceFolderWithResources = Prisma.CourseResourceFolderGetPayload<{
  include: {
    resources: true
  }
}>;

export async function getCourseResourceFolders(courseId: string) {
  try {
    const folders = await prisma.courseResourceFolder.findMany({
      where: { courseId },
      orderBy: { order: 'asc' },
      include: {
        resources: true
      }
    })
    
    return folders
  } catch (error) {
    console.log(error)
    return []
  }
}

export async function getCourseResources(courseId: string) {
  try {
    const resources = await prisma.courseResource.findMany({
      where: { courseId },
      orderBy: { order: 'asc' },
      take: 5
    })
    
    return resources
  } catch (error) {
    console.log(error)
    return []
  }
}

export async function getResource(resourceId: string) {
  try {
    const resource = await prisma.courseResource.findUnique({
      where: { id: resourceId },
      include: {
        course: true,
      }
    })
    
    return resource
  } catch (error) {
    console.log(error)
    return null
  }
}