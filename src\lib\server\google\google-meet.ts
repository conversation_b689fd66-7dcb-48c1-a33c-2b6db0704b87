'use server'

import { calendar_v3, google } from 'googleapis'

interface CreateMeetingParams {
  title: string
  description?: string
  startTime: Date
  endTime: Date
  attendees?: string[]
}

const SCOPES = [
  "https://www.googleapis.com/auth/calendar",
  "https://www.googleapis.com/auth/calendar.events",
];

const CALENDAR_ID = process.env.CALENDAR_ID;

async function initGoogleCalendar(): Promise<calendar_v3.Calendar> {
  const rawCreds = process.env.SERVICE_ACCOUNT_CREDS;
  if (!rawCreds) {
    throw new Error("Missing SERVICE_ACCOUNT_CREDS environment variable");
  }

  const credentials = JSON.parse(
    Buffer.from(rawCreds, "base64").toString("utf-8")
  );

  const auth = new google.auth.GoogleAuth({
    credentials,
    scopes: SCOPES,
    clientOptions: {
      // The email that the service account will impersonate
      subject: process.env.GOOGLE_ACCOUNT_EMAIL,
    },
  });

  return google.calendar({ version: "v3", auth });
}

export async function scheduleMeeting(params: CreateMeetingParams) {
  try {
    const calendar = await initGoogleCalendar();

    console.log(calendar.context._options.auth)

    const event: calendar_v3.Schema$Event = {
      summary: params.title,
      description: params.description,
      start: {
        dateTime: params.startTime.toISOString(),
        timeZone: 'UTC',
      },
      end: {
        dateTime: params.endTime.toISOString(),
        timeZone: 'UTC',
      },
      attendees: params.attendees?.map(email => ({ email })) || undefined,
      conferenceData: {
        createRequest: {
          requestId: `meet-${Date.now()}-${Math.random().toString(36).substring(7)}`,
          conferenceSolutionKey: { type: "hangoutsMeet" },
        },
      },
      reminders: {
        useDefault: false,
        overrides: [
          { method: 'email', minutes: 24 * 60 },
          { method: 'popup', minutes: 10 },
        ],
      },
    };

    const response = await calendar.events.insert({
      calendarId: CALENDAR_ID!,
      conferenceDataVersion: 1,
      requestBody: event,
      sendUpdates: "all",
    });

    const meetingData = response.data
    const meetUrl = meetingData.conferenceData?.entryPoints?.find(
      entry => entry.entryPointType === 'video'
    )?.uri

    return {
      eventId: meetingData.id,
      meetUrl,
      meetId: meetingData.conferenceData?.conferenceId
    };
  } catch (error) {
    console.error('Error creating Google Meet:', error)
    throw new Error('Failed to create Google Meet')
  }
}

export async function deleteMeeting(eventId: string) {
  try {
    const calendar = await initGoogleCalendar();

    await calendar.events.delete({
      calendarId: CALENDAR_ID!,
      eventId,
    });
  } catch (error) {
    console.error('Error deleting Google Meet:', error)
    throw new Error('Failed to delete Google Meet')
  }
}