import SigninForm from "@/components/form/SigninForm";
import { auth } from "../../../../auth";
import { redirect } from "next/navigation";

export default async function SignInPage({
  searchParams,
}: {
  searchParams: Promise<{ [key: string]: string | undefined }>;
}) {
  const { type } = await searchParams;
  const formType = type === "ep" ? "ep" : "lc";

  const session = await auth();

  if (session && session.user.role === "ADMIN") {
    redirect("/dashboard");
  } else if (session && session.user.role === "STUDENT") {
    redirect("/students/dashboard");
  }

  return (
    <div>
      <SigninForm type={formType} />
    </div>
  );
}
