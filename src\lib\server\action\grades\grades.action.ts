'use server'

import prisma from "@/lib/prisma"
import { revalidatePath } from "next/cache";
import { TGradeForm } from "./grades.schema";


export async function createGrade(sessionId: string, data: TGradeForm) {
  try {
    await prisma.grade.create({
      data: {
        name: data.name,
        description: data.description,
        order: await getNextGradeOrder(),
        session: { connect: { id: sessionId } },
      },
    })
    
    revalidatePath('/admin/grades')

    return { success: true, message: 'Grade created' }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to create grade' }
  }
}

export async function updateGrade(gradeId: string, data: TGradeForm) {
  try {
    await prisma.grade.update({
      where: { id: gradeId },
      data: {
        name: data.name,
        description: data.description,
      },
    })

    revalidatePath('/admin/grades')

    return { success: true, message: 'Grade updated' }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to update grade' }
  }
}

export async function deleteGrade(gradeId: string) {
  try {
    await prisma.grade.delete({
      where: { id: gradeId }
    })
    
    revalidatePath('/admin/grades')
    revalidatePath('/admin/courses')
    
    return { success: true }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to delete grade' }
  }
}

async function getNextGradeOrder(): Promise<number> {
  const lastGrade = await prisma.grade.findFirst({
    orderBy: { order: 'desc' },
    select: { order: true },
  });
  return lastGrade && typeof lastGrade.order === 'number' ? lastGrade.order + 1 : 1;
}

