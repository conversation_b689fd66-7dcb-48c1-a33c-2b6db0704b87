import {
  <PERSON>,
  Card<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>Title,
  CardDescription,
} from "@/components/ui/card";
import AssessmentForm from "../../forms/AssessmentForm";
import { getAssessment } from "@/lib/server/action/courses/modules/assessments";

export default function AssessmentSettingsCard({
  assessment,
  moduleId,
}: {
  assessment: Awaited<ReturnType<typeof getAssessment>>;
  moduleId: string;
}) {
  return (
    <div className="lg:col-span-1">
      <Card className="py-4">
        <CardHeader>
          <CardTitle>Assessment Settings</CardTitle>
          <CardDescription>Configure your assessment details</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <AssessmentForm
            assessmentData={
              assessment
                ? {
                    passingScore: assessment.passingScore,
                    maxStudentQuestions: assessment.maxStudentQuestions,
                    instructions: assessment.instructions || "",
                  }
                : undefined
            }
            moduleId={moduleId}
            assessmentId={assessment?.id}
          />

          <div className="pt-4 border-t">
            <AssInfo
              title="Total Questions"
              value={assessment?._count.questions ?? 0}
            />
            <AssInfo
              title="Max Student Questions"
              value={assessment?.maxStudentQuestions ?? 0}
            />
            <AssInfo
              title="Passing Score"
              value={assessment?.passingScore ?? 0}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

function AssInfo({ title, value }: { title: string; value: number }) {
  return (
    <div className="flex justify-between text-sm text-gray-600">
      <span>{title}</span>
      <span>{value}</span>
    </div>
  );
}
