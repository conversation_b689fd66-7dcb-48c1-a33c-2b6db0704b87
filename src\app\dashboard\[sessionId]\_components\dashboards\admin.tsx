import { StatsCard } from "./sections/StatusCard";
import { AudienceOverview } from "./sections/AudienceOverview";
import { VisitorStats } from "./sections/VisitorStatus";
import { BrowserStats } from "./sections/BrowserStats";

export default function DashboardPage() {
  return (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <StatsCard
          title="Sessions"
          value="24k"
          icon="activity"
          change={8.5}
          changeText="New Sessions Today"
          changeType="increase"
        />
        <StatsCard
          title="Avg.Sessions"
          value="00:18"
          icon="clock"
          change={1.5}
          changeText="Weekly Avg Sessions"
          changeType="increase"
        />
        <StatsCard
          title="Bounce Rate"
          value="36.45%"
          icon="bar-chart"
          change={8}
          changeText="Up Bounce Rate Weekly"
          changeType="increase"
          increaseBad
        />
      </div>

      <div className="grid gap-6 md:grid-cols-7">
        <AudienceOverview className="md:col-span-5" />
        <VisitorStats className="md:col-span-2" />
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <BrowserStats title="Browser Used & Traffic Reports" />
        <BrowserStats title="Total Visits" type="channel" />
      </div>
    </div>
  );
}
