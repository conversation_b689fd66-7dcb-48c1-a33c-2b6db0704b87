import Image from "next/image";

interface HeroSectionProps {
  data: {
    title: string;
    subtitle: string;
    image: string;
    supportText: string;
  };
}

export default function HeroSection({ data }: HeroSectionProps) {
  return (
    <section className="w-full py-12 md:py-24 lg:py-32 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="grid items-center gap-8 lg:grid-cols-2 lg:gap-12">
          <div className="flex flex-col justify-center space-y-6">
            <div className="space-y-4">
              <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl lg:text-6xl">
                {data.title}
              </h1>
              <p className="text-lg text-gray-600 md:text-xl">
                {data.subtitle}
              </p>
            </div>
            <p className="text-sm text-gray-500 italic">{data.supportText}</p>
          </div>
          <div className="flex justify-center lg:justify-end">
            <div className="relative">
              <div className="absolute inset-0 bg-blue-100 rounded-full transform scale-110 -z-10"></div>
              <Image
                src={data.image || "/images/placeholder.svg"}
                alt="Woman learning online"
                width={400}
                height={400}
                className="rounded-full object-cover"
                priority
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
