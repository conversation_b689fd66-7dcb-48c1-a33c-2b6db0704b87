'use server';

import prisma from '@/lib/prisma';
import bcrypt from 'bcrypt';
import { TAdminForm, TAdminPassword, TUpdateAdminForm } from './admins.schema';
import { revalidatePath } from 'next/cache';


export async function createAdmin(data: TAdminForm) {
  try {
    const existingUser = await prisma.user.findUnique({
      where: { email: data.email },
    });

    if (existingUser) {
      return { success: false, error: "User with this email already exists" };
    }

    const hashedPassword = await bcrypt.hash(data.password!, 10);

    const admin = await prisma.adminProfile.create({
      data: {
        password: hashedPassword,
        user: {
          create: {
            firstName: data.firstName,
            lastName: data.lastName,
            email: data.email,
            phone: data.phone,
            role: "ADMIN" as const,
            status: "APPROVED" as const,
          },
        },
      },
      include: {
        user: true,
      },
    });

    revalidatePath('/dashboard/admins')

    return { success: true, message: 'Admin created', admin };
  } catch (error) {
    console.error("Error creating admin:", error);
    return { success: false, error: "Failed to create admin" };
  }
}

export async function updateAdmin(adminId: string, data: Partial<TUpdateAdminForm>) {
  try {
    const admin = await prisma.adminProfile.update({
      where: { id: adminId },
      data: {
        user: {
          update: {
            firstName: data.firstName,
            lastName: data.lastName,
            email: data.email,
            phone: data.phone,
            status: data.status,
            avatar: data.avatarUrl,
          },
        },
      },
      include: {
        user: true,
      },
    });

    revalidatePath('/dashboard/admins')

    return { success: true, message: 'Admin updated', admin };
  } catch (error) {
    console.error("Error updating admin:", error);
    return { success: false, error: "Failed to update admin" };
  }
}

export async function updateAdminPassword(adminId: string, data: TAdminPassword) {
  try {
    const hashedPassword = await bcrypt.hash(data.password, 10);

    const admin = await prisma.adminProfile.update({
      where: { id: adminId },
      data: {
        password: hashedPassword,
      },
      include: {
        user: true,
      },
    });

    revalidatePath('/dashboard/admins')

    return { success: true, admin };
  } catch (error) {
    console.error("Error updating admin password:", error);
    return { success: false, error: "Failed to update admin password" };
  }
}

export const deleteAdmin = async (adminId: string) => {
  try {
    const admin = await prisma.adminProfile.delete({
      where: { id: adminId },
    });
    await prisma.user.delete({
      where: { id: admin.userId },
    });

    revalidatePath('/dashboard/admins')

    return { success: true };
  } catch (error) {
    console.error("Error deleting admin:", error);
    return { success: false, error: "Failed to delete admin" };
  }
};
