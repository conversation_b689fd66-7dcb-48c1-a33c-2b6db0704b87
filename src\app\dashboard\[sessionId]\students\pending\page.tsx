import PageWrapper from "../../_components/layout/PageWrapper";
import TableSearch from "../../_components/table/TableSearch";
import { DataTable } from "../../_components/table/data-table";
import { columns } from "./columns";
import Pagination from "../../_components/table/Pagination";
import { TableFilter } from "../../_components/table/TableFilter";
import { Suspense } from "react";
import Loading from "../../_components/Loading";
import { getStudents } from "@/lib/server/action/students";

const breadcrumbItems = [
  { label: "Home", href: "/dashboard" },
  { label: "Student", href: "#" },
  { label: "Pending Request" },
];

async function SuspendedComponent({
  searchParams,
  params,
}: {
  searchParams: Promise<{ [key: string]: string | undefined }>;
  params: Promise<{ sessionId: string }>;
}) {
  const { sessionId } = await params;
  const { page, search, sortby } = await searchParams;
  const currentPage = page ? +page : 1;
  const { students, total } = await getStudents({
    sessionId,
    page: currentPage,
    search: search || "",
    sortby: sortby || "",
    status: "PENDING",
  });

  return (
    <>
      <DataTable columns={columns} data={students} />
      <Pagination page={currentPage} count={total} />
    </>
  );
}

const PendingUserPage = ({
  searchParams,
  params,
}: {
  searchParams: Promise<{ [key: string]: string | undefined }>;
  params: Promise<{ sessionId: string }>;
}) => {
  const filterItems = [
    { name: "Name", value: "name" },
    { name: "Email", value: "email" },
    { name: "Status", value: "status" },
    { name: "School", value: "school" },
    { name: "Grade", value: "grade" },
    { name: "Course", value: "course" },
  ];

  return (
    <PageWrapper
      pgTitle="Pending User Requests"
      breadcrumbItems={breadcrumbItems}
    >
      {/* TOP */}
      <div className="flex justify-between items-center gap-4 w-full md:w-auto">
        <TableSearch />
        <div className="flex items-center gap-4 self-end">
          <TableFilter filterItems={filterItems} />
        </div>
      </div>
      <Suspense fallback={<Loading />}>
        <SuspendedComponent searchParams={searchParams} params={params} />
      </Suspense>
    </PageWrapper>
  );
};

export default PendingUserPage;
