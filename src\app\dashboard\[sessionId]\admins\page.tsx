import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import PageWrapper from "../_components/layout/PageWrapper";
import { Suspense } from "react";
import Loading from "../_components/Loading";
import Pagination from "../_components/table/Pagination";
import { DataTable } from "../_components/table/data-table";
import TableSearch from "../_components/table/TableSearch";
import { TableFilter } from "../_components/table/TableFilter";
import { getAdmins } from "@/lib/server/action/admins";
import { columns } from "./column";

const breadcrumbItems = [{ label: "Home", href: "/admin" }, { label: "Admin" }];

async function SuspendedComponent({
  searchParams,
}: {
  searchParams: Promise<{ [key: string]: string | undefined }>;
}) {
  const { page, search, sortby } = await searchParams;
  const currentPage = page ? +page : 1;
  const { admins, total } = await getAdmins({
    page: currentPage,
    search: search || "",
    sortby: sortby || "",
  });

  return (
    <>
      <DataTable columns={columns} data={admins} />
      <Pagination page={currentPage} count={total} />
    </>
  );
}

async function RenderButton({
  params,
}: {
  params: Promise<{ sessionId: string }>;
}) {
  const { sessionId } = await params;
  return (
    <Button size="sm" asChild>
      <Link href={`/dashboard/${sessionId}/admins/new`}>Add Admin</Link>
    </Button>
  );
}

export default function AdminsPage({
  searchParams,
  params,
}: {
  searchParams: Promise<{ [key: string]: string | undefined }>;
  params: Promise<{ sessionId: string }>;
}) {
  const filterItems = [
    { name: "Name", value: "name" },
    { name: "Email", value: "email" },
    { name: "Status", value: "status" },
  ];

  return (
    <PageWrapper
      pgTitle="Manage Admin"
      breadcrumbItems={breadcrumbItems}
      headerButton={<RenderButton params={params} />}
    >
      {/* TOP */}
      <div className="flex justify-between items-center gap-4 w-full md:w-auto">
        <TableSearch />
        <div className="flex items-center gap-4 self-end">
          <TableFilter filterItems={filterItems} />
        </div>
      </div>
      <Suspense fallback={<Loading />}>
        <SuspendedComponent searchParams={searchParams} />
      </Suspense>
    </PageWrapper>
  );
}
