import ProfileForm from "@/components/form/ProfileForm";
import PageWrapper from "../_components/layout/PageWrapper";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { formatDate } from "@/lib/formatDate";
import ChangePasswordForm from "@/components/form/ChangePasswordForm";
import ProfilePhotoCard from "../_components/cards/ProfilePhotoCard";
import { Suspense } from "react";
import { auth } from "../../../../../auth";
import { redirect } from "next/navigation";
import { getAdmin } from "@/lib/server/action/admins";

const breadcrumbItems = [
  { label: "Home", href: "/user" },
  { label: "Profile" },
];

async function SuspendedComponent() {
  const session = await auth();
  const user = await getAdmin(session?.user.profileId as string);

  if (!session || !session.user || !user) {
    redirect("/sign-in");
  }

  const userData = {
    firstName: user.user.firstName,
    lastName: user.user.lastName,
    email: user.user.email,
    phone: user.user.phone || "",
  };

  return (
    <div className="grid gap-5 md:grid-cols-[1fr_250px]">
      <Tabs defaultValue="personal" className="w-full">
        <TabsList className="mb-2 w-full md:w-fit">
          <TabsTrigger className="px-4 flex-1 md:flex-initial" value="personal">
            Personal Info
          </TabsTrigger>
          <TabsTrigger className="px-4 flex-1 md:flex-initial" value="account">
            Account
          </TabsTrigger>
        </TabsList>

        <TabsContent value="personal" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Personal Information</CardTitle>
              <CardDescription>
                Update your personal details here.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ProfileForm userData={userData} userId={user.id} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="account" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Account Settings</CardTitle>
              <CardDescription>
                Manage your account preferences.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ChangePasswordForm userEmail={user.user.email} />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <div className="space-y-5">
        <ProfilePhotoCard
          user={{
            id: user.id,
            firstName: user.user.firstName,
            lastName: user.user.lastName,
            email: user.user.email,
            phone: user.user.phone || "",
            avatar: user.user.avatar || "",
            role: user.user.role,
            createdAt: user.user.createdAt,
          }}
        />

        <Card>
          <CardHeader>
            <CardTitle>Account Status</CardTitle>
            <CardDescription>Your current account status</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Status</span>
                <span className="text-sm font-medium text-green-500">
                  Active
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Role</span>
                <span className="text-sm font-medium">Administrator</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Member Since</span>
                <span className="text-sm font-medium">
                  {formatDate(user.user.createdAt)}
                </span>
              </div>
              {/* <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Last Login</span>
                <span className="text-sm font-medium">Today, 10:30 AM</span>
              </div> */}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

const ProfilePage = () => {
  return (
    <PageWrapper pgTitle="Profile" breadcrumbItems={breadcrumbItems}>
      <Suspense fallback={"Loading..."}>
        <SuspendedComponent />
      </Suspense>
    </PageWrapper>
  );
};

export default ProfilePage;
