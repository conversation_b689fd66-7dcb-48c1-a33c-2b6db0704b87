import { transporter } from "../mailTransporter";

export async function expiredCodeMail(email: string, firstName: string) {
  await transporter.sendMail({
    from: process.env.SMTP_USER,
    to: email,
    subject: 'CEAP-NCR Virtue Series - Login Code Expired',
    text: `Dear ${firstName},\n\nWe wanted to inform you that your login code has expired. Our admin team will reactivate your code, and you will receive an email notification once it has been reactivated.\n\nThank you for your patience, and please feel free to reach out if you have any questions.\n\nBest regards,\nThe CEAP-NCR Virtue Series Team`,
  });  
}

export async function reactivatedCodeMail({email, firstName, loginCode, link}: {email: string, firstName: string, loginCode: string, link: string}) {
  await transporter.sendMail({
    from: process.env.SMTP_USER,
    to: email,
    subject: 'CEAP-NCR Approval - Your Registration is Complete',
    text: `Dear ${firstName},\n\nWe are pleased to inform you that your login code has been reactivated. You can now log in using the following code: ${loginCode}\n\nPlease follow this link to complete your login:\n\n${link}\n\nIf you encounter any issues or need assistance, please feel free to reach out.\n\nBest regards,\nThe CEAP-NCR Virtue Series Team`,
  });   
}