import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Search, Upload } from "lucide-react";
import CourseFolders from "../../sections/folders-section";
import RecentSection from "../../sections/recent-section";
import {
  getCourseResourceFolders,
  getCourseResources,
} from "@/lib/server/action/courses/resources";

export default async function FileManagementTab({
  courseId,
}: {
  courseId: string;
}) {
  const [folders, resources] = await Promise.all([
    getCourseResourceFolders(courseId),
    getCourseResources(courseId),
  ]);

  return (
    <div className="space-y-6">
      <div className="flex lg:items-center justify-between flex-col lg:flex-row gap-3">
        <h2 className="text-2xl font-bold">File Management</h2>
        <div className="flex items-center gap-4">
          <div className="relative flex w-full">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input placeholder="Search files" className="pl-10 w-64 flex-1" />
          </div>
          <Button>
            <Upload className="w-4 h-4" />
            Upload files
          </Button>
        </div>
      </div>

      <div className="space-y-6">
        <CourseFolders courseId={courseId} folders={folders} />
        <RecentSection recentFiles={resources} />
      </div>
    </div>
  );
}
