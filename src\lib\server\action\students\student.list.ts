'use server'

import { appConfig } from "@/config/app";
import prisma from "@/lib/prisma";
import { Prisma, UserStatus } from "@prisma/client";


export type StudentWithUserAndSchool = Prisma.StudentProfileGetPayload<{
  include: {
    user: {
      select: {
        firstName: true,
        lastName: true,
        email: true,
        phone: true,
        status: true,
        avatar: true,
      }
    };
    school: {
      select: {
        name: true,
        id: true
      }
    };
    grade: {
      select: {
        name: true,
        id: true
      }
    };
    enrollment: {
      select: {
        id: true,
        course: {
          select: {
            title: true,
            id: true
          }
        }
      }
    }
  };
}>;


export async function getStudents({
  sessionId,
  page,
  search,
  sortby,
  status = "APPROVED",
}: {
  sessionId: string;
  page: number;
  search?: string;
  sortby?: string;
    status?: UserStatus;
}): Promise<{
  students: StudentWithUserAndSchool[]; // Corrected type here
  total: number;
}> {
  const whereClause: Prisma.StudentProfileWhereInput = {};
  const orderByClause: Prisma.StudentProfileOrderByWithRelationInput[] = [];

  whereClause.user = { status };
  whereClause.sessionId = sessionId;

  if (search) {
    whereClause.OR = [
      { user: { firstName: { contains: search } } },
      { user: { lastName: { contains: search } } },
      { user: { email: { contains: search } } },
      { school: { name: { contains: search } } },
      { grade: { name: { contains: search } } },
      { enrollment: {  course: { title: { contains: search } } } },
    ];
  }

  if (sortby) {
    switch (sortby) {
      case "name":
        orderByClause.push({ user: { firstName: "asc" } });
        break;
      case "email":
        orderByClause.push({ user: { email: "asc" } });
        break;
      case "status":
        orderByClause.push({ user: { status: "asc" } });
        break;
      case "school":
        orderByClause.push({ school: { name: "asc" } });
        break;
      case "grade":
        orderByClause.push({ grade: { name: "asc" } });
        break;
      default:
        orderByClause.push({ user: { firstName: "asc" } });
        break;
    }
  } else {
    orderByClause.push({ user: { firstName: "asc" } });
  }

  const queryOptions: Prisma.StudentProfileFindManyArgs = {
    include: {
      user: {
        select: {
          firstName: true,
          lastName: true,
          email: true,
          phone: true,
          status: true,
          avatar: true,
        }
      },
      school: {
        select: {
          name: true,
          id: true
        }
      },
      grade: {
        select: {
          name: true,
          id: true
        }
      },
      enrollment: {
        select: {
          id: true,
          course: {
            select: {
              title: true,
              id: true
            }
          }
        }
      }
    },
    where: whereClause,
    orderBy: orderByClause.length > 0 ? orderByClause : [{ user: { firstName: "asc" } }],
    skip: (page - 1) * appConfig.ITEMS_PER_PAGE,
    take: appConfig.ITEMS_PER_PAGE,
  };

  try {
    const students = await prisma.studentProfile.findMany(queryOptions) as StudentWithUserAndSchool[];

    const total = await prisma.studentProfile.count({
      where: whereClause,
    });

    return { students, total };
  } catch (error) {
    console.error("Error fetching students:", error);
    throw new Error("Failed to fetch students.");
  }
}

export async function getStudentProfile(studentId: string) {
  return await prisma.studentProfile.findUnique({
    where: { id: studentId },
    include: {
      user: {
        select: {
          firstName: true,
          lastName: true,
          email: true,
          phone: true,
          status: true,
          avatar: true,
        }
      },
      grade: {
        select: {
          name: true,
          id: true
        }
      },
      school: {
        select: {
          name: true,
          id: true
        }
      },
      enrollment: {
        include: {
          course: true
        }
      }
    }
  })
}

// Student Dashboard Actions
export async function getStudentDashboard(studentId: string) {
  const student = await prisma.studentProfile.findUnique({
    where: { id: studentId },
    include: {
      user: {
        select: {
          firstName: true,
          lastName: true,
          email: true,
          phone: true,
          status: true,
          avatar: true,
        }
      },
      grade: {
        select: {
          name: true,
          id: true
        }
      },
      school: {
        select: {
          name: true,
          id: true
        }
      },
      enrollment: {
        select: {
          course: {
            select: {
              title: true,
              id: true
            }
          }
        }
      }
    }
  })
  
  return student
}

export async function getStudentCourseDetails(courseId: string, studentId: string) {
  const course = await prisma.course.findUnique({
    where: { id: courseId },
    include: {
      _count: {
        select: {
          enrollments: true,
          modules: true
        }
      },
      grade: {
        select: {
          name: true,
        }
      },
      modules: {
        include: {
          resources: true,
          assessment: {
            include: {
              attempts: {
                where: { studentId },
                orderBy: { attemptNumber: 'desc' }
              }
            }
          },
          progress: {
            where: { studentId }
          }
        },
        orderBy: { order: 'asc' }
      },
      teacherAssignments: {
        select: {
          role: true,
          teacher: {
            select: {
              user: {
                select: {
                  firstName: true,
                  lastName: true,
                  email: true
                }
              }
            }
          }
        }
      },
      enrollments: {
        where: { studentId },
        select: {
          id: true,
          isActive: true,
          progress: true,
        }
      }
    },
  })
  
  return course
}

export async function checkStudent(studentId: string) {
  try {
    const student = await prisma.studentProfile.findUnique({
      where: { id: studentId },
      select: {
        id: true
      }
    })
    
    return student
  } catch (error) {
    console.log(error)
    return null
  }
}

export async function checkStudentLogin(studentId: string) {
  try {
    const activeSession = await prisma.session.findFirst({
      where: { isActive: true }
    })
    if (!activeSession) {
      return null
    }

    const student = await prisma.studentProfile.findUnique({
      where: { id: studentId, sessionId: activeSession.id },
      select: {
        id: true
      }
    })

    return student
  } catch (error) {
    console.log(error)
    return null
  }
}