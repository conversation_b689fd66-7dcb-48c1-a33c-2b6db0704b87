import { Button } from "@/components/ui/button";
import StudentTable from "../../StudentTable";
import StudentFilter from "../../filters/student-filter";
import { getSchoolOptions } from "@/lib/server/action/schools";
import { getCourseStudents } from "@/lib/server/action/courses";

export async function StudentsTab({
  courseId,
  sessionId,
}: {
  courseId: string;
  sessionId: string;
}) {
  const [courseStudentsData, schools] = await Promise.all([
    getCourseStudents(courseId),
    getSchoolOptions(sessionId),
  ]);

  return (
    <div className="space-y-6">
      <div className="flex lg:items-center justify-between flex-col lg:flex-row gap-3">
        <h2 className="text-2xl font-bold">List of Students</h2>
        <div className="flex items-center gap-4">
          <Button className="bg-primary hover:bg-blue-700">
            View progress report
          </Button>
        </div>
      </div>

      <StudentFilter schools={schools} />

      <StudentTable courseStudents={courseStudentsData} />
    </div>
  );
}
