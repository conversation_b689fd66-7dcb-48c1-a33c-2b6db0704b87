import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Folder } from "lucide-react";

type FolderCardProps = {
  id: string;
  name: string;
  resources: {
    id: string;
    title: string;
    type: string;
    size: string;
    downloadUrl: string;
  }[];
};

export default function FolderCard({ folder }: { folder: FolderCardProps }) {
  return (
    <Card className="hover:shadow-md transition-shadow cursor-pointer py-0">
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Folder className="w-8 h-8 text-primary" />
            <div>
              <h4 className="font-medium">{folder.name}</h4>
              <p className="text-sm text-gray-500">
                {folder.resources.length} files
              </p>
            </div>
          </div>
          <Badge variant="secondary">{folder.resources.length}</Badge>
        </div>
      </CardContent>
    </Card>
  );
}
