import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import React from "react";
import Link from "next/link";
import CreateCourse from "@/app/dashboard/[sessionId]/courses/new/_components/CreateCourse";

export default async function NewCoursePage({
  params,
}: {
  params: Promise<{ sessionId: string }>;
}) {
  const { sessionId } = await params;
  return (
    <>
      <Button variant="outline" asChild>
        <Link href={`/dashboard/${sessionId}/courses`}>
          <ArrowLeft className="w-4 h-4" />
          Back to Courses
        </Link>
      </Button>

      <CreateCourse sessionId={sessionId} />
    </>
  );
}
