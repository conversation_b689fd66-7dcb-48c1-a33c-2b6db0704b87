"use client";

import { useState, useMemo } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  <PERSON><PERSON><PERSON>,
  PlayCircle,
  PuzzleIcon as Quiz,
  Video,
  Lock,
} from "lucide-react";
import { QuizModal } from "../AssessmentModal";
import { StudentModule } from "@/lib/server/action/courses/modules";
import { cn } from "@/lib/utils";
import VideoPlayer from "@/components/shared/VideoPlayer";
import { trackLessonVideoProgress } from "@/lib/server/action/students/modules/lesson/lesson.action";
import { toast } from "sonner";
import { startAssessment } from "@/lib/server/action/students/modules/assessments";
import { trackModuleProgress } from "@/lib/server/action/students/progress-tracking/progress-tracking.action";

interface ModulesGridProps {
  studentId: string;
  modules: StudentModule[];
}

export function ModulesGrid({ studentId, modules }: ModulesGridProps) {
  const [selectedModule, setSelectedModule] = useState<StudentModule | null>(
    null
  );
  const [showQuiz, setShowQuiz] = useState(false);
  const [showVideo, setShowVideo] = useState(false);
  const [assessment, setAssessment] = useState<{
    attemptId: string;
  } | null>(null);

  // Process modules to determine which ones should be locked
  const processedModules = useMemo(() => {
    return modules.map((module, index) => {
      // First module is always unlocked
      if (index === 0) {
        return { ...module, isLocked: false };
      }

      // Check if previous module is completed
      const prevModule = modules[index - 1];
      const isPrevModuleCompleted =
        prevModule.progress >= 100 || prevModule.completedAt !== null;

      return {
        ...module,
        isLocked: !isPrevModuleCompleted,
      };
    });
  }, [modules]);

  const handleStartQuiz = async (module: StudentModule) => {
    setSelectedModule(module);
    setShowQuiz(true);

    const result = await startAssessment({
      assessmentId: module.assessment.id as string,
      studentId,
    });
    if (result.success) {
      setAssessment({
        attemptId: result.attempt!.id,
      });
      toast.success(result.message);
    } else {
      toast.error(result.error);
    }
  };

  const handleStartVideo = async (module: StudentModule) => {
    setSelectedModule(module);
    await trackLessonVideoProgress({
      lessonId: module.lesson.id as string,
      studentId,
      videoDuration: module.lesson.video?.duration as string,
      progress: 0,
      isCompleted: false,
    });

    setShowVideo(true);
  };

  const handleProceed = async () => {
    if (!selectedModule) return;

    await trackLessonVideoProgress({
      lessonId: selectedModule.lesson.id as string,
      studentId,
      videoDuration: selectedModule.lesson.video?.duration as string,
      progress: 100,
      isCompleted: true,
    });

    if (
      selectedModule.assessment &&
      selectedModule.assessment.questionCount > 0
    ) {
      await trackModuleProgress(studentId, selectedModule.id, 50);
    } else {
      await trackModuleProgress(studentId, selectedModule.id, 100, true);
    }

    setShowVideo(false);
  };

  const handleViewDoc = async (module: StudentModule) => {
    setSelectedModule(module);
    if (module.assessment && module.assessment.questionCount > 0) {
      await trackModuleProgress(studentId, module.id, 50);
    } else {
      await trackModuleProgress(studentId, module.id, 100, true);
    }
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {processedModules.map((module) => (
          <Card
            key={module.id}
            className={cn(
              "gap-0 relative h-fit",
              module.bestAssessment &&
                module.bestAssessment.status === "COMPLETED"
                ? module.bestAssessment.passed
                  ? "border-green-500"
                  : "border-red-500"
                : ""
            )}
          >
            {module.bestAssessment &&
            module.bestAssessment.status === "COMPLETED" ? (
              <Badge
                className={cn(
                  "absolute top-3 right-3",
                  module.bestAssessment.passed
                    ? "bg-green-200 text-green-500"
                    : "bg-red-200 text-red-500"
                )}
                variant={module.isLocked ? "secondary" : "default"}
              >
                {module.bestAssessment.passed ? "Passed" : "Failed"}
              </Badge>
            ) : null}

            {module.isLocked && (
              <div className="absolute inset-0 bg-black/50 flex items-center justify-center rounded-xl cursor-not-allowed">
                <Lock className="h-10 w-10 text-white" />
              </div>
            )}

            <CardHeader>
              <div className="flex items-start justify-between">
                <CardTitle className="text-lg line-clamp-2">
                  {module.title}
                </CardTitle>
              </div>
            </CardHeader>

            <CardContent className="space-y-4 mt-auto">
              <p className="text-gray-600 text-sm line-clamp-3">
                {module.description}
              </p>

              {module.bestAssessment &&
                module.bestAssessment.status === "COMPLETED" && (
                  <div>
                    <p className="text-xs font-medium text-muted-foreground">
                      Score: {module.bestAssessment.score}
                    </p>
                    <p className="text-xs font-medium text-muted-foreground">
                      Passed: {module.bestAssessment.passed ? "Yes" : "No"}
                    </p>
                  </div>
                )}

              <div className="flex items-center gap-4 text-sm text-gray-500">
                {module.lesson.type === "VIDEO" ? (
                  <div className="flex items-center gap-1 text-xs">
                    <Video className="h-4 w-4" />
                    {module.lesson.video?.duration} mins
                  </div>
                ) : null}
                <div className="flex items-center gap-1 text-xs">
                  <BookOpen className="h-4 w-4" />
                  {module.assessment.maxStudentQuestions} questions
                </div>
              </div>

              {/* {module.progress > 0 && (
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Progress</span>
                    <span className="font-medium">{module.progress}%</span>
                  </div>
                  <Progress value={module.progress} className="h-2" />
                </div>
              )} */}

              <div className="flex flex-col gap-2">
                {module.lesson.video && (
                  <Button
                    className="w-full cursor-pointer"
                    variant={
                      module.lesson.video.isCompleted ? "secondary" : "default"
                    }
                    onClick={() => handleStartVideo(module)}
                  >
                    <PlayCircle className="h-4 w-4 mr-2" />
                    {!module.lesson.video.isCompleted
                      ? "Continue"
                      : module.lesson.video.isCompleted
                      ? "Replay Video"
                      : "Watch Video"}
                  </Button>
                )}

                {module.lesson.type === "DOC" && (
                  <Button
                    className="w-full cursor-pointer"
                    variant="default"
                    onClick={() => handleViewDoc(module)}
                  >
                    View Document
                  </Button>
                )}

                {module.assessment && module.assessment.questionCount > 0 && (
                  <Button
                    variant="outline"
                    className="w-full cursor-pointer"
                    onClick={() => handleStartQuiz(module)}
                    disabled={
                      module.lesson.type === "VIDEO" &&
                      !module.lesson.video?.isCompleted
                    }
                  >
                    <Quiz className="h-4 w-4 mr-2" />
                    {module.assessment.status === "COMPLETED"
                      ? "Retake Assessment"
                      : "Take Assessment"}
                    {module.lesson.video &&
                      !module.lesson.video.isCompleted && (
                        <Badge variant="secondary" className="ml-2">
                          Locked
                        </Badge>
                      )}
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {showQuiz && selectedModule && (
        <QuizModal
          assessmentId={selectedModule.assessment.id as string}
          attemptId={assessment?.attemptId as string}
          studentId={studentId}
          moduleId={selectedModule.id}
          passingScore={selectedModule.assessment.passingScore}
          maxStudentQuestions={selectedModule.assessment.maxStudentQuestions}
          isOpen={showQuiz}
          title={selectedModule.title}
          onClose={() => {
            setShowQuiz(false);
            setSelectedModule(null);
          }}
        />
      )}

      {showVideo && selectedModule && selectedModule.lesson.video && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/90">
          <div className="w-full max-w-4xl bg-gray-200 rounded-lg overflow-hidden">
            <VideoPlayer
              url={selectedModule.lesson.video.url as string}
              videoId={selectedModule.lesson.video.url as string}
              title={selectedModule.lesson.title as string}
              showProceedButton
              onCancel={() => setShowVideo(false)}
              onProceed={handleProceed}
              hasWatched={selectedModule.lesson.video.hasWatched}
              controls={selectedModule.lesson.video.hasWatched}
            />
          </div>
        </div>
      )}
    </div>
  );
}
