"use client";

import CustomAlertDialog from "@/components/shared/CustomAlertDialog";
import { CustomSheet } from "@/components/shared/CustomSheet";
import { CustomDropdown } from "@/components/shared/Dropdown";
import { Button } from "@/components/ui/button";
import {
  DropdownMenuItem,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { ColumnDef } from "@tanstack/react-table";
import { EllipsisVertical } from "lucide-react";
import { toast } from "sonner";
import { deleteGrade, GradeWithCourses } from "@/lib/server/action/grades";
import GradeForm from "@/components/form/GradeForm";

export const columns: ColumnDef<GradeWithCourses>[] = [
  {
    accessorKey: "grade.name",
    header: "name",
    cell: ({ row }) => row.original.name,
  },
  {
    accessorKey: "grade.description",
    header: "description",
    cell: ({ row }) => (
      <div className="w-40 truncate">{row.original.description}</div>
    ),
  },
  {
    accessorKey: "grade.courses",
    header: "No. Courses",
    cell: ({ row }) => row.original._count.courses,
  },
  {
    accessorKey: "action",
    cell: ({ row }) => {
      const grade = row.original;

      return (
        <CustomDropdown
          trigger={
            <Button size="icon" variant="ghost">
              <EllipsisVertical />
            </Button>
          }
        >
          <DropdownMenuItem asChild>
            <CustomSheet title="Edit Grade" trigger="Edit" asChild={false}>
              <GradeForm
                gradeData={{
                  name: grade.name,
                  description: grade.description || "",
                }}
                gradeId={grade.id}
              />
            </CustomSheet>
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem asChild>
            <CustomAlertDialog
              asChild={false}
              trigger="Delete"
              onConfirm={async () => {
                const res = await deleteGrade(grade.id);
                if (res.success) {
                  toast.success("Grade deleted");
                } else {
                  toast.error("Failed to delete grade");
                }
              }}
            />
          </DropdownMenuItem>
        </CustomDropdown>
      );
    },
  },
];
