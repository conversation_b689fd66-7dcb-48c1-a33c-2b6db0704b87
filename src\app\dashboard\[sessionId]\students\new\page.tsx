import { StudentForm } from "@/components/form/StudentForm";
import { Button } from "@/components/ui/button";
import { ChevronLeft } from "lucide-react";
import Link from "next/link";

export default async function NewStudentPage({
  params,
}: {
  params: Promise<{ sessionId: string }>;
}) {
  const { sessionId } = await params;
  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2 -mt-2">
        <Button variant="outline" size="sm" asChild>
          <Link href={`/dashboard/${sessionId}/students`}>
            <ChevronLeft className="h-4 w-4" />
            Back to Students
          </Link>
        </Button>
      </div>
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Create Student</h1>
      </div>
      <div className="rounded-lg border bg-card p-6">
        <StudentForm isAdmin={true} sessionId={sessionId} />
      </div>
    </div>
  );
}
