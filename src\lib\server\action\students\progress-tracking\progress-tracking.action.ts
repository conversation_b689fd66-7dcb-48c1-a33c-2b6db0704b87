'use server'

import prisma from "@/lib/prisma"
import { revalidatePath } from "next/cache"


export async function trackModuleProgress(studentId: string, moduleId: string, progress: number, isCompleted?: boolean) {
  try {
    const progressRecord = await prisma.studentModuleProgress.upsert({
      where: {
        studentId_moduleId: {
          studentId,
          moduleId
        }
      },
      update: {
        progress: Math.max(progress, 0),
        lastAccessed: new Date(),
        completedAt: progress >= 100 ? new Date() : null,
        isCompleted
      },
      create: {
        studentId,
        moduleId,
        progress: Math.max(progress, 0),
        lastAccessed: new Date(),
        completedAt: progress >= 100 ? new Date() : null
      }
    })
    
    // Update course enrollment progress
    await updateCourseProgress(studentId, moduleId)

    revalidatePath('/student/dashboard/modules')
    
    return { success: true, progressRecord }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to track progress' }
  }
}

async function updateCourseProgress(studentId: string, moduleId: string) {
  const moduleD = await prisma.module.findUnique({
    where: { id: moduleId },
    include: { course: true }
  })
  
  if (!moduleD) return
  
  const totalModules = await prisma.module.count({
    where: { courseId: moduleD.courseId }
  })
  
  const completedModules = await prisma.studentModuleProgress.count({
    where: {
      studentId,
      module: { courseId: moduleD.courseId },
      completedAt: { not: null }
    }
  })
  
  const courseProgress = (completedModules / totalModules) * 100
  
  await prisma.enrollment.updateMany({
    where: {
      studentId,
      courseId: moduleD.courseId
    },
    data: {
      progress: courseProgress,
      completedAt: courseProgress >= 100 ? new Date() : null
    }
  })
}