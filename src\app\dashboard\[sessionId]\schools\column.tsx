"use client";

import SchoolForm from "@/components/form/SchoolForm";
import CustomAlertDialog from "@/components/shared/CustomAlertDialog";
import { CustomSheet } from "@/components/shared/CustomSheet";
import { CustomDropdown } from "@/components/shared/Dropdown";
import { Button } from "@/components/ui/button";
import {
  DropdownMenuItem,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { deleteSchool, SchoolWithExtra } from "@/lib/server/action/schools";
import { ColumnDef } from "@tanstack/react-table";
import { EllipsisVertical } from "lucide-react";
import { toast } from "sonner";

export const columns: ColumnDef<SchoolWithExtra>[] = [
  {
    accessorKey: "name",
    header: "Name",
  },
  {
    accessorKey: "students",
    header: "No. Students",
    cell: ({ row }) => row.original._count.students,
  },
  {
    accessorKey: "teachers",
    header: "No. Teachers",
    cell: ({ row }) => row.original._count.teachers,
  },
  {
    accessorKey: "description",
    header: "Description",
    cell: ({ row }) => (
      <div className="w-40 truncate">{row.original.description}</div>
    ),
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const school = row.original;
      return (
        <CustomDropdown
          trigger={
            <Button size="icon" variant="ghost">
              <EllipsisVertical />
            </Button>
          }
        >
          <DropdownMenuItem asChild>
            <CustomSheet title="Edit School" trigger="Edit" asChild={false}>
              <SchoolForm
                schoolData={{
                  name: school.name,
                  description: school.description || "",
                }}
                schoolId={school.id}
              />
            </CustomSheet>
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem asChild>
            <CustomAlertDialog
              asChild={false}
              trigger="Delete"
              onConfirm={async () => {
                const res = await deleteSchool(school.id);
                if (res.success) {
                  toast.success("School deleted");
                } else {
                  toast.error("Failed to delete school");
                }
              }}
            />
          </DropdownMenuItem>
        </CustomDropdown>
      );
    },
  },
];
