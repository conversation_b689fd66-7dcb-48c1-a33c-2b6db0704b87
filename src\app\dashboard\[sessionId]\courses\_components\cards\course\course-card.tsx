"use client";

import { Card, CardContent } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Users } from "lucide-react";
import { CourseWithExtras } from "@/lib/server/action/courses";
import Image from "next/image";
import { useRouter } from "next/navigation";

export default function CourseCard({
  course,
  primaryTeacherName,
  sessionId,
}: {
  course: CourseWithExtras;
  primaryTeacherName: string;
  sessionId: string;
}) {
  const router = useRouter();

  const getStatusColor = (status: CourseWithExtras["status"]) => {
    switch (status) {
      case "PUBLISHED":
        return "bg-green-100 text-green-800";
      case "DRAFT":
        return "bg-yellow-100 text-yellow-800";
      case "ARCHIVED":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <Card
      className="relative p-0 gap-0 cursor-pointer hover:shadow-lg transition-all duration-200"
      onClick={() => {
        router.push(`/dashboard/${sessionId}/courses/${course.id}`);
      }}
    >
      {/* Course Cover */}
      <div className="relative rounded-t-lg overflow-hidden h-48">
        {true ? (
          <Image
            src={"/images/placeholder.svg"}
            alt={course.title}
            fill
            className="object-cover"
          />
        ) : (
          <>
            <div className="absolute inset-0 bg-gradient-to-r from-blue-600/90 to-blue-800/90" />
            <div className="relative z-10 flex items-center justify-center h-full">
              <h3 className="text-2xl font-bold text-white text-center px-4">
                COURSE PHOTO
              </h3>
            </div>
            <div className="absolute inset-0 opacity-20">
              <div className="absolute top-4 left-4 w-16 h-16 bg-white/10 rounded-full blur-lg" />
              <div className="absolute bottom-4 right-4 w-20 h-20 bg-white/10 rounded-full blur-lg" />
            </div>
          </>
        )}

        {/* Status Badge */}
        <div className="absolute bottom-3 left-3">
          <Badge className={getStatusColor(course.status)}>
            {course.status.charAt(0).toUpperCase() + course.status.slice(1)}
          </Badge>
        </div>
      </div>

      {/* Course Content */}
      <CardContent className="p-4">
        <div className="space-y-3">
          <div>
            <h3 className="font-bold text-lg mb-1">{course.title}</h3>
            <p className="text-gray-600 text-sm line-clamp-2">
              {course.description}
            </p>
          </div>

          <div className="flex items-center gap-2 text-xs text-gray-500">
            <Badge variant="outline">{course.grade.name}</Badge>
          </div>

          <div className="flex items-center justify-between pt-2">
            <div className="flex items-center gap-2">
              <Avatar className="w-6 h-6">
                <AvatarImage src="/placeholder.svg?height=24&width=24" />
                <AvatarFallback>
                  {primaryTeacherName
                    .split(" ")
                    .map((n) => n[0])
                    .join("")}
                </AvatarFallback>
              </Avatar>
              <span className="text-sm text-gray-700">
                {primaryTeacherName}
              </span>
            </div>
            <div className="flex items-center gap-1 text-sm text-gray-500">
              <Users className="w-4 h-4" />
              {course._count.enrollments}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
