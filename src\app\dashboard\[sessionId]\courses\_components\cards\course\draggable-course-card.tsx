/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Users, GripVertical, MoreHorizontal, Edit, Eye } from "lucide-react";
import {
  DropdownMenuItem,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { DragDropContext, Droppable, Draggable } from "@hello-pangea/dnd";
import { useOptimistic, useTransition } from "react";
import {
  CourseWithExtras,
  deleteCourse,
  reorderCourse,
} from "@/lib/server/action/courses";
import Image from "next/image";
import Link from "next/link";
import { CustomDropdown } from "@/components/shared/Dropdown";
import CustomAlertDialog from "@/components/shared/CustomAlertDialog";
import { toast } from "sonner";

export default function DraggableCourseCard({
  courses,
}: {
  courses: CourseWithExtras[];
}) {
  const [, startTransition] = useTransition();

  const [optimisticCourses, swapOptimisticCourses] = useOptimistic(
    courses,
    (state, { sourceCourseId, destinationCourseId }) => {
      const sourceIndex = state.findIndex((c) => c.id === sourceCourseId);
      const destinationIndex = state.findIndex(
        (c) => c.id === destinationCourseId
      );

      const newState = [...state];
      const [reorderedItem] = newState.splice(sourceIndex, 1);
      newState.splice(destinationIndex, 0, reorderedItem);

      return newState;
    }
  );

  const handleDragEnd = async (result: any) => {
    const sourceCourseId = result.draggableId;
    const destinationCourseId = courses[result.destination.index].id;
    startTransition(async () => {
      swapOptimisticCourses({ sourceCourseId, destinationCourseId });
    });

    await reorderCourse(optimisticCourses.map((c) => c.id));
  };

  const getStatusColor = (status: CourseWithExtras["status"]) => {
    switch (status) {
      case "PUBLISHED":
        return "bg-green-100 text-green-800";
      case "DRAFT":
        return "bg-yellow-100 text-yellow-800";
      case "ARCHIVED":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <DragDropContext onDragEnd={handleDragEnd}>
      <Droppable droppableId="courses" direction="vertical">
        {(provided) => (
          <div
            {...provided.droppableProps}
            ref={provided.innerRef}
            className="flex flex-col flex-grow gap-6"
          >
            {optimisticCourses.map((course, index) => {
              const primaryTeacher = course.teacherAssignments.find(
                (ta) => ta.role === "PRIMARY"
              );
              const primaryTeacherName = primaryTeacher
                ? primaryTeacher.teacher.user.firstName +
                  " " +
                  primaryTeacher.teacher.user.lastName
                : "N/A";
              return (
                <Draggable
                  key={course.id}
                  draggableId={course.id}
                  index={index}
                >
                  {(provided, snapshot) => (
                    <Card
                      ref={provided.innerRef}
                      {...provided.draggableProps}
                      className={`relative overflow-hidden hover:shadow-lg transition-all duration-200 p-0 ${
                        snapshot.isDragging ? "rotate-3 shadow-2xl" : ""
                      }`}
                    >
                      {/* Drag Handle */}
                      <div
                        {...provided.dragHandleProps}
                        className="absolute top-2 right-2 z-10 p-2 rounded-lg bg-white/80 hover:bg-muted transition-colors cursor-pointer"
                      >
                        <GripVertical className="w-4 h-4 text-gray-500" />
                      </div>

                      {/* Course Actions */}
                      <div className="absolute top-2 left-2 z-10">
                        <CustomDropdown
                          trigger={
                            <Button size="icon" variant="ghost">
                              <MoreHorizontal />
                            </Button>
                          }
                        >
                          <DropdownMenuItem asChild>
                            <Link href={`/dashboard/courses/${course.id}`}>
                              <Eye className="w-4 h-4 mr-2" />
                              View Course
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem asChild>
                            <Link
                              href={`/dashboard/courses/${course.id}/settings`}
                            >
                              <Edit className="w-4 h-4 mr-2" />
                              Edit Course
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem asChild>
                            <CustomAlertDialog
                              asChild={false}
                              trigger="Delete Course"
                              title="Delete Course"
                              description="Are you sure you want to delete this course?"
                              onConfirm={async () => {
                                const res = await deleteCourse(course.id);
                                if (res.success) {
                                  toast.success("Course deleted");
                                } else {
                                  toast.error("Failed to delete course");
                                }
                              }}
                            />
                          </DropdownMenuItem>
                        </CustomDropdown>
                      </div>

                      {/* Course Content */}
                      <CardContent className="flex flex-col lg:flex-row px-0 w-full">
                        <div className="relative h-48 lg:h-auto lg:w-48 shrink-0 bg-gradient-to-r from-blue-600 via-blue-700 to-blue-800">
                          {true ? (
                            <Image
                              src={"/images/placeholder.svg"}
                              alt={course.title}
                              fill
                              className="object-cover"
                            />
                          ) : (
                            <>
                              <div className="absolute inset-0 bg-gradient-to-r from-blue-600/90 to-blue-800/90" />
                              <div className="relative z-10 flex items-center justify-center h-full">
                                <h3 className="text-2xl font-bold text-white text-center px-4">
                                  COURSE PHOTO
                                </h3>
                              </div>
                              <div className="absolute inset-0 opacity-20">
                                <div className="absolute top-4 left-4 w-16 h-16 bg-white/10 rounded-full blur-lg" />
                                <div className="absolute bottom-4 right-4 w-20 h-20 bg-white/10 rounded-full blur-lg" />
                              </div>
                            </>
                          )}

                          {/* Status Badge */}
                          <div className="absolute bottom-3 left-3">
                            <Badge className={getStatusColor(course.status)}>
                              {course.status.charAt(0).toUpperCase() +
                                course.status.slice(1)}
                            </Badge>
                          </div>
                        </div>
                        <div className="space-y-3 py-4 px-4 lg:px-6 w-full">
                          <div>
                            <h3 className="font-bold text-lg mb-1">
                              {course.title}
                            </h3>
                            <p className="text-gray-600 text-sm line-clamp-2">
                              {course.description}
                            </p>
                          </div>

                          <div className="flex items-center gap-2 text-xs text-gray-500">
                            <Badge variant="outline">{course.grade.name}</Badge>
                          </div>

                          <div className="flex items-center justify-between pt-2">
                            <div className="flex items-center gap-2">
                              <Avatar className="w-6 h-6">
                                <AvatarImage src="/placeholder.svg?height=24&width=24" />
                                <AvatarFallback>
                                  {primaryTeacherName
                                    .split(" ")
                                    .map((n) => n[0])
                                    .join("")}
                                </AvatarFallback>
                              </Avatar>
                              <span className="text-sm text-gray-700">
                                {primaryTeacherName}
                              </span>
                            </div>
                            <div className="flex items-center gap-1 text-sm text-gray-500">
                              <Users className="w-4 h-4" />
                              {"student"}
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </Draggable>
              );
            })}
            {provided.placeholder}
          </div>
        )}
      </Droppable>
    </DragDropContext>
  );
}
