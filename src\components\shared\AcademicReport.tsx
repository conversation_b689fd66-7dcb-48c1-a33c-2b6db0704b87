"use client";

import { Card, CardContent } from "@/components/ui/card";
import { getStudentReport } from "@/lib/server/action/students/reports/reports.list";
import { useEffect, useState } from "react";

interface ModuleData {
  name: string;
  attempts: number;
  score: number;
}

interface AchievementTest {
  name: string;
  score: number;
}

interface StudentReport {
  courseName: string;
  modules: ModuleData[];
  totalModuleScore: number;
  achievementTest: AchievementTest[];
  totalActivityScore: number;
}

export default function AcademicReport({ studentId }: { studentId: string }) {
  const [studentReport, setStudentReport] = useState<StudentReport | null>(
    null
  );
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    async function fetchStudentReport() {
      try {
        setIsLoading(true);
        const report = await getStudentReport(studentId);
        setStudentReport(report);
        setIsLoading(false);
      } catch (error) {
        console.log(error);
        setIsLoading(false);
      }
    }
    fetchStudentReport();
  }, [studentId]);

  if (!studentReport || isLoading) {
    return <div className="text-center">Loading...</div>;
  }

  return (
    <div className="w-full space-y-6">
      {/* Header */}
      <div className="text-center py-3 bg-gray-100 rounded-lg w-full">
        <h1 className="text-lg font-bold text-gray-900 tracking-wide">
          {studentReport.courseName}
        </h1>
      </div>

      {/* First Table - Module Details */}
      <Card className="py-0 w-full overflow-hidden">
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="bg-gray-200">
                  <th className="text-left p-3 font-semibold text-gray-700 border-r border-gray-300">
                    Module name
                  </th>
                  <th className="text-center p-3 font-semibold text-gray-700 border-r border-gray-300 w-24">
                    Attempts
                  </th>
                  <th className="text-center p-3 font-semibold text-gray-700 w-24">
                    Score
                  </th>
                </tr>
              </thead>
              <tbody>
                {!studentReport.modules ? (
                  <tr>
                    <td colSpan={3} className="p-3 text-center">
                      No modules found.
                    </td>
                  </tr>
                ) : (
                  <>
                    {studentReport.modules.map((module, index) => (
                      <tr
                        key={index}
                        className="border-b border-gray-200 hover:bg-gray-50"
                      >
                        <td className="p-3 text-sm text-gray-900 border-r border-gray-200">
                          {module.name}
                        </td>
                        <td className="p-3 text-sm text-gray-700 text-center border-r border-gray-200">
                          {module.attempts}
                        </td>
                        <td className="p-3 text-sm text-gray-700 text-center">
                          {module.score}
                        </td>
                      </tr>
                    ))}{" "}
                  </>
                )}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Second Table - Activity Grades */}
      <Card className="py-0 w-full overflow-hidden">
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="bg-gray-200">
                  <th className="text-left p-3 font-semibold text-gray-700 border-r border-gray-300">
                    Activity name
                  </th>
                  <th className="text-center p-3 font-semibold text-gray-700 w-32">
                    Grade
                  </th>
                </tr>
              </thead>
              <tbody>
                {!studentReport.achievementTest ? (
                  <tr>
                    <td colSpan={2} className="p-3 text-center">
                      No achievement test found.
                    </td>
                  </tr>
                ) : (
                  <>
                    {studentReport.achievementTest.map((module, index) => (
                      <tr
                        key={index}
                        className="border-b border-gray-200 hover:bg-gray-50"
                      >
                        <td className="p-3 text-sm text-gray-900 border-r border-gray-200">
                          {module.name}
                        </td>
                        <td className="p-3 text-sm text-gray-700 text-center">
                          {module.score}
                        </td>
                      </tr>
                    ))}{" "}
                  </>
                )}
                <tr className="bg-gray-50">
                  <td className="p-3 text-sm font-semibold text-gray-900 border-r border-gray-200">
                    Average grade:
                  </td>
                  <td className="p-3 text-sm font-bold text-green-600 text-center">
                    90%
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
