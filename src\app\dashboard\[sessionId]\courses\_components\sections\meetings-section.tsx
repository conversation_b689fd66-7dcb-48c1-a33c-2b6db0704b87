import {
  deleteMeeting,
  joinMeeting,
  MeetingWithHostAndParticipants,
} from "@/lib/server/action/courses/virtual-classrooms";
import MeetingCard from "../cards/course/meeting-card";
import { toast } from "sonner";

export default function MeetingsSection({
  meetings,
}: {
  meetings: MeetingWithHostAndParticipants[];
}) {
  const handleJoinMeeting = async (meetingId: string) => {
    const res = await joinMeeting(meetingId);
    if (res.success) {
      window.open(res.meetUrl!, "_blank");
    } else {
      toast.error(res.error);
    }
  };

  const handleViewMeeting = (meetingId: string) => {
    // Navigate to meeting details page
    console.log("View meeting:", meetingId);
  };

  const handleDeleteMeeting = async (meetingId: string) => {
    const res = await deleteMeeting(meetingId);
    if (res.success) {
      toast.success(res.message);
    } else {
      toast.error(res.error);
    }
  };

  const ongoingMeetings = meetings.filter((m) => m.status === "ONGOING");
  const upcomingMeetings = meetings.filter((m) => m.status === "SCHEDULED");
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {ongoingMeetings.map((meeting) => (
        <MeetingCard
          key={meeting.id}
          meeting={meeting}
          onJoin={handleJoinMeeting}
          onView={handleViewMeeting}
        />
      ))}

      {upcomingMeetings.map((meeting) => (
        <MeetingCard
          key={meeting.id}
          meeting={meeting}
          onJoin={handleJoinMeeting}
          onView={handleViewMeeting}
          onDelete={handleDeleteMeeting}
        />
      ))}
    </div>
  );
}
