"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { Form } from "@/components/ui/form";
import { Button } from "../ui/button";
import { Loader } from "lucide-react";
import { toast } from "sonner";
import {
  gradeSchema,
  TGradeForm,
} from "@/lib/server/action/grades/grades.schema";
import { FormInputField } from "../form-element/input-field";
import { FormTextareaField } from "../form-element/text-area";
import { createGrade, updateGrade } from "@/lib/server/action/grades";
import { useParams } from "next/navigation";

export default function GradeForm({
  gradeId,
  gradeData,
}: {
  gradeId?: string;
  gradeData?: TGradeForm;
}) {
  // get session id from url
  const params = useParams<{ sessionId: string }>();
  if (!params.sessionId) throw new Error("Session ID is required");

  const form = useForm<TGradeForm>({
    resolver: zodResolver(gradeSchema),
    defaultValues: gradeData ?? {
      name: "",
      description: "",
    },
    mode: "onChange",
  });

  const onSubmit = async (values: TGradeForm) => {
    const res = gradeData
      ? await updateGrade(gradeId as string, values)
      : await createGrade(params.sessionId, values);
    if (res.success) {
      toast.success(res.message);
    } else {
      toast.error(res.error);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <FormInputField
          control={form.control}
          name="name"
          label="Name"
          placeholder="Enter grade name"
        />
        <FormTextareaField
          control={form.control}
          name="description"
          label="Description"
          placeholder="Enter grade description"
        />
        <div className="flex justify-end gap-2">
          <Button type="submit" disabled={form.formState.isSubmitting}>
            {form.formState.isSubmitting ? (
              <>
                <Loader /> {gradeData ? "Updating Grade" : "Create Grade"}
              </>
            ) : (
              <>{gradeData ? "Update Grade" : "Create Grade"}</>
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}
