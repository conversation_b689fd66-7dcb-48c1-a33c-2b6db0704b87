import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Users, Video, ExternalLink } from "lucide-react";
import Image from "next/image";

export default function MainDashboard() {
  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="flex items-center gap-2">
        <h1 className="text-3xl font-bold">Hello User</h1>
        <span className="text-3xl">👋</span>
      </div>
      <p className="text-muted-foreground">{"Let's learn something today"}</p>

      <div className="grid grid-cols-1 lg:grid-cols-5 gap-6">
        {/* My Course */}
        <Card className="col-span-3 gap-0 py-4">
          <CardHeader>
            <CardTitle className="text-lg">My course</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex gap-4">
              <Image
                src="/images/placeholder.svg"
                alt="Course"
                width={300}
                height={123}
                className="w-auto h-42 object-cover rounded-lg"
              />
              <div className="w-full flex flex-col justify-between">
                <div>
                  <Badge className="bg-green-500">Grade 7</Badge>
                  <p className="text-sm text-muted-foreground line-clamp-4 mt-2">
                    Lorem ipsum dolor sit amet consectetur adipisicing elit.
                    Alias modi quos neque cupiditate magnam atque amet fuga.
                    Fugiat, exercitationem perspiciatis iusto unde facilis
                    quisquam iure corrupti quasi! Deleniti, non cum!
                  </p>
                </div>

                <div className="flex flex-col md:flex-row gap-2 md:items-center md:justify-between border-t pt-2">
                  <div className="flex items-center gap-2">
                    <Avatar className="w-6 h-6">
                      <AvatarImage src="/placeholder.svg?height=24&width=24" />
                      <AvatarFallback className="p-2">BO</AvatarFallback>
                    </Avatar>
                    <span className="text-sm">Brian O&apos;conner</span>
                  </div>
                  <div className="flex items-center gap-1 text-sm text-muted-foreground">
                    <Users className="w-4 h-4" />
                    <span>1K students</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Ongoing Conference */}
        <Card className="col-span-2 gap-4">
          <CardHeader>
            <CardTitle className="text-lg">Ongoing</CardTitle>
            <p className="text-sm text-muted-foreground">
              June 10, 2025 10:00 AM
            </p>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <h3 className="font-semibold">Conference call</h3>
                <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                  <Video className="w-4 h-4 text-white" />
                </div>
              </div>
              <p className="text-sm text-muted-foreground">
                This conference call is for student who will participate into
                the incoming activities
              </p>
              <div className="flex gap-2">
                <Button variant="outline" size="sm">
                  <ExternalLink className="w-4 h-4 mr-2" />
                  View
                </Button>
                <Button size="sm">Join</Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Upcoming Schedules */}
        <Card className="col-span-2">
          <CardHeader>
            <CardTitle>Upcoming schedules</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-2">Name</th>
                    <th className="text-left p-2">Description</th>
                    <th className="text-left p-2">Platform</th>
                    <th className="text-left p-2">Date and time</th>
                  </tr>
                </thead>
                <tbody>
                  {[
                    {
                      name: "Conference meeting",
                      desc: "This meeting is all about...",
                      platform: "Google Meet",
                      date: "July 23, 2025 9:00 AM",
                    },
                    {
                      name: "Conference meeting",
                      desc: "This meeting is all about...",
                      platform: "Zoom",
                      date: "July 23, 2025 9:00 AM",
                    },
                    {
                      name: "Conference meeting",
                      desc: "This meeting is all about...",
                      platform: "Zoom",
                      date: "July 23, 2025 9:00 AM",
                    },
                    {
                      name: "Conference meeting",
                      desc: "This meeting is all about...",
                      platform: "Google Meet",
                      date: "July 23, 2025 9:00 AM",
                    },
                  ].map((item, index) => (
                    <tr key={index} className="border-b">
                      <td className="p-2">{item.name}</td>
                      <td className="p-2 text-muted-foreground">{item.desc}</td>
                      <td className="p-2">{item.platform}</td>
                      <td className="p-2 text-muted-foreground">{item.date}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        {/* Assessment Evaluation */}
        <div className="col-span-1 border-2 border-gray-200 rounded-xl shadow-sm overflow-hidden">
          <div className="flex justify-between gap-4 h-full">
            <div className="p-4 size-full">
              <h2 className="text-lg">My Assessment Evaluation</h2>
              <div className="flex flex-col items-center justify-center gap-4 size-full">
                <div className="relative w-24 h-24">
                  <svg
                    className="w-24 h-24 transform -rotate-90"
                    viewBox="0 0 36 36"
                  >
                    <path
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                      fill="none"
                      stroke="#e5e7eb"
                      strokeWidth="2"
                    />
                    <path
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                      fill="none"
                      stroke="#3b82f6"
                      strokeWidth="2"
                      strokeDasharray="100, 100"
                    />
                  </svg>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <span className="text-xl font-bold">100%</span>
                  </div>
                </div>
                <div className="w-full">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Completed</span>
                    <span className="font-bold text-blue-600">10</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Not Yet</span>
                    <span className="font-bold">0</span>
                  </div>
                </div>
              </div>
            </div>
            <div className="bg-muted h-full flex items-center">
              <div className="text-center p-4">
                <span className="text-sm text-muted-foreground whitespace-nowrap">
                  INITIAL GRADE:
                </span>
                <div className="text-4xl font-bold text-green-500">94</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Assessment Report */}
      <Card>
        <CardHeader>
          <CardTitle>INTRODUCTION TO COMPUTER REPORT</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <h4 className="font-semibold mb-2">Module name</h4>
                <div className="space-y-1">
                  {[
                    "Pastoral life, CEAP Advocates and Catholic Social Teachings",
                    "Christian Living Education Diagnostic Exam",
                    "Foundational Teachings, Salvation History, Faith and Revelation, Sacred Scriptures",
                    "Spiritual Life, Prayer, Sacraments, Lives of Saints, Popular Piety",
                    "Pastoral Life, Praying for and Aly Kapwa, Parish and Pastoral Life, Global Compact on Education",
                  ].map((module, index) => (
                    <div key={index} className="text-muted-foreground">
                      {module}
                    </div>
                  ))}
                </div>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Time</h4>
                <div className="space-y-1">
                  {Array(5)
                    .fill("54 minutes")
                    .map((time, index) => (
                      <div key={index} className="text-muted-foreground">
                        {time}
                      </div>
                    ))}
                </div>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Score</h4>
                <div className="space-y-1">
                  {Array(5)
                    .fill("90 / 100")
                    .map((score, index) => (
                      <div key={index} className="text-muted-foreground">
                        {score}
                      </div>
                    ))}
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
