import Image from "next/image";
import Link from "next/link";
import React from "react";

const Logo = ({ variant = "default" }: { variant?: "default" | "footer" }) => {
  return (
    <Link href={"/"} className="flex items-center space-x-2 shrink-0">
      <div
        className={`rounded-full p-1 shrink-0 ${
          variant === "footer" ? "bg-white" : ""
        }`}
      >
        <span
          className={`font-bold text-xl ${
            variant === "footer" ? "text-primary" : "text-white"
          }`}
        >
          <Image src="/images/logo.png" alt="Logo" width={30} height={30} />
        </span>
      </div>
      <span className="font-semibold text-xl">
        MaPSA -
        <span className={variant === "footer" ? "" : "text-primary"}>LMS</span>
      </span>
    </Link>
  );
};

export default Logo;
