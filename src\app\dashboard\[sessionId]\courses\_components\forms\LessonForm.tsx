"use client";

import type React from "react";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Progress } from "@/components/ui/progress";
import {
  createLesson,
  deleteLesson,
  update<PERSON>esson,
} from "@/lib/server/action/courses/modules/lessons";
import { toast } from "sonner";
import CustomAlertDialog from "@/components/shared/CustomAlertDialog";
import { Loader } from "lucide-react";
import { FormSelectField } from "@/components/form-element/select-field";
import { FormInputField } from "@/components/form-element/input-field";
import { FormTextareaField } from "@/components/form-element/text-area";
import { Form } from "@/components/ui/form";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  lessonSchema,
  TLessonForm,
} from "@/lib/server/action/courses/modules/lessons/lesson.schema";

interface LessonData {
  title: string;
  description: string;
  videoUrl: string;
  duration: string;
}

export default function LessonForm({
  lessonId,
  lessonData,
  moduleId,
}: {
  lessonId?: string;
  lessonData?: LessonData;
  moduleId: string;
}) {
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const [upload, setUpload] = useState<File | null>(null);

  const form = useForm<TLessonForm>({
    resolver: zodResolver(lessonSchema),
    defaultValues: lessonData ?? {
      title: "",
      description: "",
      type: "VIDEO",
    },
    mode: "onChange",
  });

  const type = form.watch("type");

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsUploading(true);
    setUploadProgress(0);

    // Simulate upload progress
    const interval = setInterval(() => {
      setUploadProgress((prev) => {
        if (prev >= 100) {
          clearInterval(interval);
          setIsUploading(false);
          // Simulate video URL update
          // setFormData((prevData) => ({
          //   ...prevData,
          //   videoUrl: URL.createObjectURL(file),
          //   duration: "12:45", // Simulated duration
          // }));
          setUpload(file);
          return 100;
        }
        return prev + 10;
      });
    }, 200);
  };

  const onSubmit = async (values: TLessonForm) => {
    const res = lessonId
      ? await updateLesson(lessonId, values)
      : await createLesson(moduleId, values);
    if (res.success) {
      toast.success(res.message);
    } else {
      toast.error(res.error);
    }
  };

  const handleDelete = async () => {
    const res = await deleteLesson(lessonId as string);
    if (res.success) {
      toast.success(res.message);
    } else {
      toast.error(res.error);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormSelectField
          control={form.control}
          name="type"
          label="Type"
          placeholder="Select a type"
          options={[
            { value: "VIDEO", label: "Video" },
            { value: "DOC", label: "Document" },
          ]}
        />
        <FormInputField
          control={form.control}
          name="title"
          label="Title"
          placeholder="Enter lesson title"
        />
        <FormTextareaField
          control={form.control}
          name="description"
          label="Description"
          placeholder="Enter lesson description"
        />

        {/* Video Upload */}
        <div className="space-y-2">
          <Label htmlFor="file">File Upload</Label>
          <div className="space-y-3">
            <Input
              id="file"
              type="file"
              accept={type === "VIDEO" ? "video/*" : "*"}
              onChange={handleFileUpload}
              disabled={isUploading}
              className="file:mr-4 file:py-1 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-primary-foreground hover:file:bg-primary/90"
              required
            />

            {isUploading && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Uploading File...</span>
                  <span>{uploadProgress}%</span>
                </div>
                <Progress value={uploadProgress} className="w-full" />
              </div>
            )}
          </div>
          {upload?.name && (
            <div className="">
              <p className="text-sm text-muted-foreground">
                File URL: <span className="text-primary">{upload?.name}</span>
              </p>
            </div>
          )}
        </div>

        <div className="flex items-center gap-2 pt-4">
          {lessonId ? (
            <CustomAlertDialog
              title="Delete Lesson"
              description="This action will remove lesson data for this module. Are you sure you want to delete this lesson?"
              trigger={<Button variant="destructive">Delete Lesson</Button>}
              onConfirm={handleDelete}
              className="ml-auto"
            />
          ) : null}
          <Button type="submit" disabled={form.formState.isSubmitting}>
            {form.formState.isSubmitting ? (
              <>
                <Loader /> {lessonId ? "Updating Lesson" : "Creating Lesson"}
              </>
            ) : (
              <>{lessonId ? "Update Lesson" : "Create Lesson"}</>
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}
