import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { CustomSheet } from "@/components/shared/CustomSheet";
import { Suspense } from "react";
import Loading from "@/app/dashboard/[sessionId]/_components/Loading";
import { getActivities } from "@/lib/server/action/courses/activities";
import { Card, CardContent } from "@/components/ui/card";
import Link from "next/link";
import ActivityForm from "@/app/dashboard/[sessionId]/courses/_components/forms/ActivityForm";

async function SuspendedComponent({
  courseId,
  sessionId,
}: {
  courseId: string;
  sessionId: string;
}) {
  const activities = await getActivities(courseId);

  return (
    <>
      <div className="space-y-4">
        {activities.map((activity, index) => {
          const isActive = activity.deadline > new Date();
          return (
            <Card
              key={activity.id}
              className={`py-0 ${!isActive ? "border border-red-200" : ""}`}
            >
              <CardContent className="px-6 py-4">
                <div className="flex flex-col md:flex-row items-start justify-between gap-4">
                  <div className="flex-1">
                    <div className="flex items-start gap-3">
                      <span className="text-lg font-medium text-gray-900">
                        {index + 1}.
                      </span>
                      <div className="flex-1">
                        <p className="text-gray-900 mb-4">
                          {activity.question}
                        </p>
                        <div className="flex items-center gap-6 text-sm text-gray-600">
                          <span>
                            📊 Total responses:{" "}
                            <span className="font-medium">
                              {activity._count.responses}
                            </span>
                          </span>
                          <span>
                            ❌ Not yet:{" "}
                            <span className="font-medium">
                              {activity.course._count.enrollments -
                                activity._count.responses}
                            </span>
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="flex md:flex-col items-center md:items-end justify-center gap-3">
                    <Link
                      href={`/dashboard/${sessionId}/courses/${courseId}/activities/${activity.id}`}
                    >
                      <Button variant="outline" size="sm">
                        View response
                      </Button>
                    </Link>
                    <p className="text-xs text-gray-500 font-medium">
                      Deadline: {activity.deadline.toLocaleDateString()}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {activities.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500 text-lg">No activity found.</p>
        </div>
      )}
    </>
  );
}

export default function ActivitiesTab({
  courseId,
  sessionId,
}: {
  courseId: string;
  sessionId: string;
}) {
  return (
    <div className="space-y-6">
      <div className="flex lg:items-center justify-between flex-col lg:flex-row gap-3">
        <h2 className="text-2xl font-bold">Activities</h2>
        <CustomSheet
          title="Add Activity"
          trigger={
            <Button size="sm">
              <Plus className="w-4 h-4" />
              Add Activity
            </Button>
          }
        >
          <ActivityForm courseId={courseId} />
        </CustomSheet>
      </div>

      <Suspense fallback={<Loading />}>
        <SuspendedComponent courseId={courseId} sessionId={sessionId} />
      </Suspense>
    </div>
  );
}
