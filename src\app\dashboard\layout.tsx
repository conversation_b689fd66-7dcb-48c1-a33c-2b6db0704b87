import { SidebarInset, Sidebar<PERSON>rovider } from "@/components/ui/sidebar";
import AppSidebar from "./[sessionId]/_components/layout/app-sidebar";
import DashboardHeader from "./[sessionId]/_components/layout/dashboard-header";
import { getActiveSession } from "@/lib/server/action/sessions";
import NewSessionForm from "@/components/form/NewSessionForm";
import { auth } from "../../../auth";
import { redirect } from "next/navigation";

const DashboardLayout = async ({ children }: { children: React.ReactNode }) => {
  const session = await auth();
  if (!session || session.user.role === "STUDENT" || !session.user.profileId) {
    redirect("/sign-in");
  }

  const activeSession = await getActiveSession();
  if (!activeSession) {
    const isAdmin = session.user.role === "ADMIN";
    return (
      <div className="flex flex-col justify-center items-center min-h-dvh w-full text-xl p-4">
        <h1 className="text-2xl font-bold">No active session found</h1>
        <p className="text-sm text-muted-foreground">
          {isAdmin
            ? "Fill in the details below to create a new session"
            : "Contact your admin"}
        </p>
        {isAdmin ? <NewSessionForm /> : null}
      </div>
    );
  }

  return (
    <SidebarProvider>
      <AppSidebar activeSessionId={activeSession.id} />

      <SidebarInset>
        <DashboardHeader
          activeSessionId={activeSession.id}
          user={session.user}
        />
        <div className="w-full max-w-[1600px] mx-auto p-6">{children}</div>
      </SidebarInset>
    </SidebarProvider>
  );
};

export default DashboardLayout;
