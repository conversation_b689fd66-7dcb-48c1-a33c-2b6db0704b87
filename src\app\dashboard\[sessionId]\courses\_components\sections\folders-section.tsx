"use client";

import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Folder, Plus } from "lucide-react";
import { CustomSheet } from "@/components/shared/CustomSheet";
import { Label } from "@/components/ui/label";
import { Fragment, useState } from "react";
import {
  CourseResourceFolderWithResources,
  createCourseResourceFolder,
} from "@/lib/server/action/courses/resources";
import { toast } from "sonner";

export default function CourseFolders({
  courseId,
  folders,
}: {
  courseId: string;
  folders: CourseResourceFolderWithResources[];
}) {
  const [isCreatingFolder, setIsCreatingFolder] = useState(false);
  const [folderName, setFolderName] = useState("");

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    try {
      setIsCreatingFolder(true);
      const res = await createCourseResourceFolder(courseId, {
        name: folderName,
      });
      if (res.success) {
        toast.success(res.message);
      } else {
        toast.error(res.error);
      }
    } catch (error) {
      console.log(error);
    } finally {
      setIsCreatingFolder(false);
    }
  };

  return (
    <div>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold">Folders</h3>
        <CustomSheet
          title="Create Folder"
          trigger={
            <Button variant="outline" size="sm">
              <Plus className="w-4 h-4" />
              New Folder
            </Button>
          }
        >
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="folder-name">Folder Name</Label>
              <Input
                placeholder="Folder name"
                className="w-full"
                value={folderName}
                onChange={(e) => setFolderName(e.target.value)}
                required
              />
            </div>
            <Button className="w-full">
              {isCreatingFolder ? "Creating..." : "Create"}
            </Button>
          </form>
        </CustomSheet>
      </div>
      {folders.length === 0 ? (
        <p className="text-sm text-muted-foreground">
          No folders found. Create a new folder.
        </p>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {folders.map((folder) => (
            <Fragment key={folder.id}>
              <FolderCard folder={folder} />
            </Fragment>
          ))}
        </div>
      )}
    </div>
  );
}

function FolderCard({ folder }: { folder: CourseResourceFolderWithResources }) {
  return (
    <Card className="hover:shadow-md transition-shadow cursor-pointer py-0">
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Folder className="w-8 h-8 text-primary" />
            <div>
              <h4 className="font-medium">{folder.name}</h4>
              <p className="text-sm text-gray-500">
                {folder.resources.length} files
              </p>
            </div>
          </div>
          <Badge variant="secondary">{folder.resources.length}</Badge>
        </div>
      </CardContent>
    </Card>
  );
}
