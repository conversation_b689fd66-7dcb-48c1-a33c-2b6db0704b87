import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search, Calendar, FileText } from "lucide-react";
import Link from "next/link";
import { getStudentActivities } from "@/lib/server/action/students/activities/activities.list";
import Pagination from "@/components/shared/Pagination";
import { auth } from "../../../../../auth";

export default async function ActivitiesList({
  searchParams,
}: {
  searchParams: Promise<{ [key: string]: string | undefined }>;
}) {
  const session = await auth();
  if (!session || !session.user) {
    return null;
  }
  const { page } = await searchParams;
  const currentPage = page ? +page : 1;

  const { activities, total } = await getStudentActivities({
    courseId: session.user.courseId as string,
    studentId: session.user.profileId,
    page: currentPage,
  });

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">List of activity/essays</h1>
        <div className="relative w-64">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
          <Input placeholder="Search" className="pl-10" />
        </div>
      </div>

      <div className="space-y-4">
        {!activities ? (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg">No activity found.</p>
          </div>
        ) : (
          <>
            {activities.map((activity, index) => {
              const graded = activity.responses[0]?.status === "GRADED";
              const expired = activity.deadline < new Date();

              return (
                <Card
                  key={activity.id}
                  className={`py-0 ${
                    expired ? "border border-red-200 cursor-not-allowed" : ""
                  }`}
                >
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-start gap-3">
                          <span className="font-semibold text-lg">
                            {index + 1}.
                          </span>
                          <div className="flex-1">
                            <p className="text-lg mb-4">{activity.question}</p>

                            <div className="flex items-center gap-6 text-sm text-muted-foreground">
                              <div className="flex items-center gap-2">
                                <FileText className="w-4 h-4" />
                                <span className="text-sm font-medium">
                                  Score:{" "}
                                  {activity.responses[0] ? (
                                    <span className="text-green-600 font-semibold">
                                      {activity.responses[0]?.score}
                                    </span>
                                  ) : (
                                    "-"
                                  )}
                                </span>
                              </div>
                              <div className="flex items-center gap-2">
                                <Calendar className="w-4 h-4" />
                                <span className="text-sm font-medium">
                                  Submitted on:{" "}
                                  {activity.responses[0]?.submittedAt.toDateString() ||
                                    "-"}
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="flex flex-col items-end gap-3">
                        {!activity.responses[0] || expired ? (
                          <Button variant="outline">
                            <Link
                              href={`/students/dashboard/activities/${activity.id}`}
                            >
                              Respond
                            </Link>
                          </Button>
                        ) : !expired ? (
                          <Button variant="outline" asChild>
                            <Link
                              href={`/students/dashboard/activities/${activity.id}?reviewed=${graded}`}
                            >
                              View response
                            </Link>
                          </Button>
                        ) : null}
                        <p className="text-xs text-muted-foreground">
                          Deadline: {activity.deadline.toDateString()}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </>
        )}
      </div>

      {/* Pagination */}
      <Pagination page={currentPage} count={total} />
    </div>
  );
}
