"use server";

import { createQuestionHash } from "@/lib/hashQuestion";
import prisma from "@/lib/prisma";
import { revalidatePath } from "next/cache";

interface Question {
  id: string;
  type: "multiple-choice" | "true-false";
  question: string;
  options?: string[];
  correctAnswer: string;
}

export async function createQuestion(assessmentId: string, data: Question) {
  try {
    const question = await prisma.question.create({
      data: {
        question: data.question,
        questionType: data.type,
        options: JSON.stringify(data.options),
        correctAnswer: data.correctAnswer,
        questionHash: createQuestionHash(data.question),
        assessmentId,
        order: await getNextQuestionOrder(assessmentId),
      },
      select: {
        assessment: {
          select: {
            module: {
              select: {
                course: {
                  select: {
                    id: true,
                  },
                },
                id: true,
              },
            },
          },
        },
      },
    });

    const courseId = question.assessment.module.course.id;
    const moduleId = question.assessment.module.id;

    revalidatePath(`/admin/courses/${courseId}/modules/${moduleId}`);
    return { success: true, message: "Question created" };
  } catch (error) {
    console.log(error);
    return { success: false, error: "Failed to create question" };
  }
}

export async function importQuestions (assessmentId: string, questions: Question[], deletePreviousQuestions: boolean) {
  try {
    if (!questions.length) {
      return { success: false, message: "No questions to import" };
    }

    // Check for duplicate questions and filter them out
    const uniqueQuestions = [];
    const duplicateQuestions = [];

    if (deletePreviousQuestions) {
      await prisma.question.deleteMany({
        where: { assessmentId }
      })
    } else {
      // Check for duplicate questions
      for (let i = 0; i < questions.length; i++) {
        const q = questions[i];
        const questionHash = createQuestionHash(q.question);
        
        // Check if question already exists in database
        const existingQuestion = await prisma.question.findUnique({
          where: { questionHash_assessmentId: { questionHash, assessmentId } },
          include: { assessment: { select: { module: { select: { title: true } } } } }
        });        
        
        if (existingQuestion) {
          duplicateQuestions.push({
            question: q.question,
            existingInQuiz: existingQuestion.assessment.module.title
          });
        } else {
          uniqueQuestions.push({
            ...q,
            questionHash
          });
        }
      }

      if (uniqueQuestions.length === 0) {
        return { success: false, message: "All questions are duplicates", duplicateQuestions };
      }
    }

    const finalQuestions = deletePreviousQuestions ? questions : uniqueQuestions;

    const createPromises = finalQuestions.map((question, index) => {
      return prisma.question.create({
        data: {
          question: question.question,
          questionType: question.type,
          options: JSON.stringify(question.options),
          correctAnswer: question.correctAnswer,
          questionHash: createQuestionHash(question.question),
          assessmentId,
          order: index,
        },
        select: {
          assessment: {
            select: {
              module: {
                select: {
                  course: {
                    select: {
                      id: true,
                    },
                  },
                  id: true,
                },
              },
            },
          },
        },
      });
    });

    const createdQuestions = await Promise.all(createPromises);

    const courseId = createdQuestions[0].assessment.module.course.id;
    const moduleId = createdQuestions[0].assessment.module.id;
    const res = duplicateQuestions.length > 0 ? { success: true, message: `${duplicateQuestions.length} duplicate questions were skipped`, duplicateQuestions } : { success: true, message: "Questions Imported"};

    revalidatePath(`/admin/courses/${courseId}/modules/${moduleId}`);
    return res;
  } catch (error) {
    console.log(error);
    return { success: false, message: "Failed to create questions" };
  }
}

export async function updateQuestion(
  questionId: string,
  data: Partial<Question>
) {
  try {
    const question = await prisma.question.update({
      where: { id: questionId },
      data: {
        question: data.question,
        questionHash: data.question? createQuestionHash(data.question) : undefined,
        questionType: data.type,
        options: JSON.stringify(data.options),
        correctAnswer: data.correctAnswer,
      },
      select: {
        assessment: {
          select: {
            module: {
              select: {
                course: {
                  select: {
                    id: true,
                  },
                },
                id: true,
              },
            },
          },
        },
      },
    });

    const courseId = question.assessment.module.course.id;
    const moduleId = question.assessment.module.id;

    revalidatePath(`/admin/courses/${courseId}/modules/${moduleId}`);
    return { success: true, message: "Question updated" };
  } catch (error) {
    console.log(error);
    return { success: false, error: "Failed to update question" };
  }
}

export async function deleteQuestion(questionId: string) {
  try {
    const question = await prisma.question.delete({
      where: { id: questionId },
      select: {
        assessment: {
          select: {
            module: {
              select: {
                course: {
                  select: {
                    id: true,
                  },
                },
                id: true,
              },
            },
          },
        },
      },
    });

    const courseId = question.assessment.module.course.id;
    const moduleId = question.assessment.module.id;

    revalidatePath(`/admin/courses/${courseId}/modules/${moduleId}`);
    return { success: true, message: "Question deleted" };
  } catch (error) {
    console.log(error);
    return { success: false, error: "Failed to delete question" };
  }
}

async function getNextQuestionOrder(assessmentId: string): Promise<number> {
  const lastQuestion = await prisma.question.findFirst({
    where: { assessmentId },
    orderBy: { order: "desc" },
  });
  return (lastQuestion?.order || 0) + 1;
}
