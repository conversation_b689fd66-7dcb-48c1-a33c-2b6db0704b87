import Logo from "@/components/shared/logo";

const footer = {
  logo: {
    text: "MaPSA Educational System",
    icon: "🎓",
  },
  contact: {
    title: "Contact Information",
    email: "<EMAIL>",
    mobile: "Mobile: +63 (02) 994-6403",
    address:
      "Address: Centro Catholic College, A. Bonifacio Ave., Centro Rizal 1920",
  },
  copyright: "© 2025 MaPSA Antipolo Educational System. All Rights Reserved",
};

export default function Footer() {
  return (
    <footer className="w-full bg-gray-100 py-12">
      <div className="container mx-auto px-4">
        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
          <div className="flex flex-col items-center md:items-start space-y-4">
            <Logo />
          </div>

          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-800">
              {footer.contact.title}
            </h3>
            <div className="space-y-2 text-gray-600">
              <p>
                <span className="font-medium">Email:</span>{" "}
                {footer.contact.email}
              </p>
              <p>{footer.contact.mobile}</p>
              <p>
                <span className="font-medium">Address:</span>{" "}
                {footer.contact.address}
              </p>
            </div>
          </div>
        </div>

        <div className="mt-8 pt-8 border-t border-gray-200 text-center">
          <p className="text-gray-600 text-sm">{footer.copyright}</p>
        </div>
      </div>
    </footer>
  );
}
