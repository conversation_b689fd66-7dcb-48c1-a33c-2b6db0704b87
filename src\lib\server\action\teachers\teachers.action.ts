'use server'

import prisma from '@/lib/prisma';
import { TTeacherForm, TUpdateTeacherForm } from './teacher.schema';
import { revalidatePath } from 'next/cache';


export async function createTeacher (data: TTeacherForm, sessionId: string, isAdmin: boolean) {
  try {
    const existingUser = await prisma.user.findUnique({
      where: { email: data.email },
    });

    if (existingUser) {
      return { success: false, message: "User with this email already exists" };
    }

    const teacher = await prisma.teacherProfile.create({
      data: {
        user: {
          create: {
            firstName: data.firstName,
            lastName: data.lastName,
            email: data.email,
            phone: data.phone,
            role: "TEACHER" as const,
            status: isAdmin ? "APPROVED" as const : "PENDING" as const,
          },
        },
        school: { connect: { id: data.school } },
        session: { connect: { id: sessionId } },
      },
      include: {
        user: true,
        school: true,
      },
    });

    if (isAdmin) return approveTeacher(teacher.id)

    revalidatePath('/dashboard/teachers')
    revalidatePath('/dashboard/codes')

    return { success: true, message: 'Teacher created' };
  } catch (error) {
    console.error("Error creating teacher:", error);
    return { success: false, message: "Failed to create teacher" };
  }
};

export async function approveTeacher(teacherId: string) {
  try {
    const teacher = await prisma.teacherProfile.findUnique({
      where: { id: teacherId },
      select: {
        userId: true,
        sessionId: true,
      },
    });

    if (!teacher) {
      return { success: false, message: "Teacher not found" };
    }

    const loginCode = `code-${Date.now().toString(36)}${Math.random().toString(36).slice(2, 8)}`

    await prisma.$transaction([
      prisma.teacherProfile.update({
        where: { id: teacherId },
        data: {
          user: {
            update: {
              status: "APPROVED" as const,
            },
          },
          loginCode,
        },
      }),
      prisma.code.create({
        data: {
          user: { connect: { id: teacher.userId } },
          code: loginCode,
          type: "TEACHER" as const,
          session: { connect: { id: teacher.sessionId } },
        },
      }),
    ])

    revalidatePath('/dashboard/teachers')

    return { success: true, message: 'Teacher approved' };
  } catch (error) {
    console.error("Error approving teacher:", error);
    return { success: false, message: "Failed to approve teacher" };
  }
};

export async function updateTeacher (teacherId: string, data: Partial<TUpdateTeacherForm>) {
  try {
    await prisma.teacherProfile.update({
      where: { id: teacherId },
      data: {
        user: {
          update: {
            firstName: data.firstName,
            lastName: data.lastName,
            email: data.email,
            phone: data.phone,
            status: data.status,
            avatar: data.avatarUrl,
          },
        },
        school: { connect: { id: data.school } },
        bio: data.bio,
        specialization: data.specialization,
      },
      include: {
        user: true,
        school: true,
      },
    });

    revalidatePath('/dashboard/teachers')

    return { success: true, message: 'Teacher updated' };
  } catch (error) {
    console.error("Error updating teacher:", error);
    return { success: false, error: "Failed to update teacher" };
  }
};

export async function rejectTeacher (teacherId: string) {
  try {
    await prisma.teacherProfile.delete({
      where: { id: teacherId },
    });

    revalidatePath('/dashboard/teachers')

    return { success: true, message: 'Teacher approval rejected' };
  } catch (error) {
    console.error("Error rejecting teacher:", error);
    return { success: false, error: "Failed to reject teacher" };
  }
};


export async function deleteTeacher (teacherId: string) {
  try {
    const teacher = await prisma.teacherProfile.delete({
      where: { id: teacherId },
    });
    await prisma.user.delete({
      where: { id: teacher.userId },
    });

    revalidatePath('/dashboard/teachers')

    return { success: true };
  } catch (error) {
    console.error("Error deleting teacher:", error);
    return { success: false, error: "Failed to delete teacher" };
  }
};
