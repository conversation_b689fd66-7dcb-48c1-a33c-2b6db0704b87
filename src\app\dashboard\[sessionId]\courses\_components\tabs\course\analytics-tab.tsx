import OverviewCard from "../../cards/course/overview-card";
import RecentActivityCard from "../../cards/course/recent-activity-card";
import TopPerformingModules from "../../cards/course/top-performing-modules";
import AssessmentOverviewCard from "../../cards/course/assessment-overview-card";
import TeacherInfoCard from "../../cards/course/teacher-info-card";
import {
  getCourseAnalytics,
  getCourseTeachers,
  getRecentActivity,
  getStudentEvaluation,
  getTopPerformingModules,
} from "@/lib/server/action/courses";
import { Suspense } from "react";

export async function CourseAnalyticsTab({
  courseId,
  sessionId,
}: {
  courseId: string;
  sessionId: string;
}) {
  const courseAnalytics = await getCourseAnalytics(courseId);
  const teacherData = getCourseTeachers(courseId);
  const studentEvaluation = getStudentEvaluation(courseId);
  const TopPerformingModulesData = getTopPerformingModules(courseId);
  const recentActivityData = getRecentActivity(courseId);

  return (
    <div className="space-y-6">
      <OverviewCard
        totalModules={courseAnalytics.totalModules}
        totalAttempts={courseAnalytics.totalAttempts}
        averageScore={Math.round(courseAnalytics.averageScore)}
        totalStudents={courseAnalytics.totalStudents}
      />

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Teacher Info and Setup */}
        <Suspense fallback={<div>Loading...</div>}>
          <TeacherInfoCard
            courseId={courseId}
            sessionId={sessionId}
            teachers={await teacherData}
          />
        </Suspense>

        {/* Center Column - Assessment Overview */}
        <Suspense fallback={<div>Loading...</div>}>
          <AssessmentOverviewCard studentEvaluation={await studentEvaluation} />
        </Suspense>

        {/* Right Column - Top Performing Modules */}
        <Suspense fallback={<div>Loading...</div>}>
          <TopPerformingModules topModules={await TopPerformingModulesData} />
        </Suspense>
      </div>

      <Suspense fallback={<div>Loading...</div>}>
        <RecentActivityCard recentActivity={await recentActivityData} />
      </Suspense>
    </div>
  );
}
