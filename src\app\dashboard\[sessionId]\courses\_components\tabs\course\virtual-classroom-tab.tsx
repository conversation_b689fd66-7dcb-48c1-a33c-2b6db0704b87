import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { CustomSheet } from "@/components/shared/CustomSheet";
import CreateMeetingForm from "../../forms/create-meeting-form";
import { getMeetings } from "@/lib/server/action/courses/virtual-classrooms";
import MeetingsSection from "../../sections/meetings-section";
import VCQuickAction from "../../sections/vc-quick-action";
import Loading from "@/app/dashboard/[sessionId]/_components/Loading";
import { Suspense } from "react";

async function SuspendedComponent({ courseId }: { courseId: string }) {
  const meetings = await getMeetings(courseId);

  if (meetings.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500 text-lg">No meeting found.</p>
      </div>
    );
  }

  return (
    <>
      <MeetingsSection meetings={meetings} />
    </>
  );
}

export function VirtualClassroomTab({ courseId }: { courseId: string }) {
  return (
    <div className="space-y-6">
      <div className="flex lg:items-center justify-between flex-col lg:flex-row gap-3">
        <h2 className="text-2xl font-bold">Virtual Classroom</h2>
        <div className="flex items-center gap-4">
          <CustomSheet
            title="Create Meeting"
            trigger={
              <Button size="sm">
                <Plus className="w-4 h-4" />
                Create Meeting
              </Button>
            }
          >
            <CreateMeetingForm courseId={courseId} />
          </CustomSheet>
        </div>
      </div>

      <Suspense fallback={<Loading />}>
        <SuspendedComponent courseId={courseId} />
      </Suspense>

      {/* Quick Actions */}
      <VCQuickAction courseId={courseId} />
    </div>
  );
}
