"use client";

import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Plus, XCircle } from "lucide-react";
import { useState } from "react";
import { QuestionModal } from "../question-modal";
import QuestionCard from "../cards/module/question-card";
import {
  createQuestion,
  deleteQuestion,
  Question,
  updateQuestion,
} from "@/lib/server/action/courses/modules/assessments/questions";
import { toast } from "sonner";

export default function QuestionSection({
  assessmentId,
  questions,
}: {
  questions: Question[];
  assessmentId?: string;
}) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingQuestion, setEditingQuestion] = useState<Question | null>(null);

  const handleAddQuestion = async (question: Question) => {
    setIsModalOpen(false);
    const action = editingQuestion
      ? () => updateQuestion(editingQuestion!.id, question)
      : () => createQuestion(assessmentId as string, question);

    const res = await action();

    if (res.success) {
      toast.success(res.message);
    } else {
      toast.error(res.error);
    }

    setEditingQuestion(null);
  };

  const handleEditQuestion = (question: Question) => {
    setEditingQuestion(question);
    setIsModalOpen(true);
  };

  const handleDeleteQuestion = async (questionId: string) => {
    const res = await deleteQuestion(questionId);
    if (res.success) {
      toast.success(res.message);
    } else {
      toast.error(res.error);
    }
  };

  return (
    <>
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Questions</CardTitle>
              <CardDescription>
                Add and manage your assessment questions
              </CardDescription>
            </div>
            {assessmentId ? (
              <Button onClick={() => setIsModalOpen(true)} size="sm">
                <Plus className="h-4 w-4" />
                Add Question
              </Button>
            ) : null}
          </div>
        </CardHeader>
        <CardContent>
          {!assessmentId ? (
            <div className="text-center py-12 text-gray-500">
              <XCircle className="size-12 mx-auto mb-3 text-red-400" />
              <h2 className="text-lg font-semibold">
                No assessment settings found.
              </h2>
              <p className="text-sm">
                Create assessment settings to get started.
              </p>
            </div>
          ) : (
            <>
              {questions.length === 0 ? (
                <div className="text-center py-12 text-gray-500">
                  <p>No questions added yet.</p>
                  <p className="text-sm">
                    Click &quot;Add Question&quot; to get started.
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {questions.map((question, index) => (
                    <QuestionCard
                      key={question.id}
                      index={index}
                      question={question}
                      handleEditQuestion={() => handleEditQuestion(question)}
                      handleDeleteQuestion={() =>
                        handleDeleteQuestion(question.id)
                      }
                    />
                  ))}
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      <QuestionModal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          setEditingQuestion(null);
        }}
        onSave={handleAddQuestion}
        editingQuestion={editingQuestion}
      />
    </>
  );
}
