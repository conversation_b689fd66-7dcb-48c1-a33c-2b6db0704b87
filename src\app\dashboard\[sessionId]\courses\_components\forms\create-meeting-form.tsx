"use client";

import { Loader } from "lucide-react";
import { createMeeting } from "@/lib/server/action/courses/virtual-classrooms";
import { toast } from "sonner";
import { FormSelectField } from "@/components/form-element/select-field";
import { FormInputField } from "@/components/form-element/input-field";
import { FormTextareaField } from "@/components/form-element/text-area";
import { FormDatePickerField } from "@/components/form-element/date-picker";
import { Button } from "@/components/ui/button";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  meetingSchema,
  TMeetingForm,
} from "@/lib/server/action/courses/virtual-classrooms/virtual-classrooms.schema";
import { Form } from "@/components/ui/form";

export default function CreateMeetingForm({ courseId }: { courseId: string }) {
  const form = useForm<TMeetingForm>({
    resolver: zodResolver(meetingSchema),
    defaultValues: {
      title: "",
      description: "",
      type: "CONFERENCE_CALL",
      scheduledAt: new Date(),
      participantEmails: [],
    },
    mode: "onChange",
  });

  const onSubmit = async (values: TMeetingForm) => {
    const res = await createMeeting(courseId, values);
    if (res.success) {
      toast.success(res.message);
    } else {
      toast.error(res.error);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormInputField
          control={form.control}
          name="title"
          label="Title"
          placeholder="Enter meeting title"
        />
        <FormTextareaField
          control={form.control}
          name="description"
          label="Description"
          placeholder="Enter meeting description"
        />
        <FormSelectField
          control={form.control}
          name="type"
          label="Type"
          placeholder="Select a type"
          options={[
            { value: "CONFERENCE_CALL", label: "Conference Call" },
            { value: "VIDEO_CALL", label: "Video Call" },
          ]}
        />
        <FormDatePickerField
          control={form.control}
          name="scheduledAt"
          label="Scheduled Time"
          placeholder="Select a scheduled time"
          description="The meeting will be scheduled at this time"
        />
        <FormInputField
          control={form.control}
          name="participantEmails"
          label="Participant Emails (Optional)"
          placeholder="<EMAIL>, <EMAIL>"
          description="Separate multiple emails with commas"
        />
        <div className="flex justify-end gap-2">
          <Button type="submit" disabled={form.formState.isSubmitting}>
            {form.formState.isSubmitting ? (
              <>
                <Loader /> Creating Meeting
              </>
            ) : (
              <>Create Meeting</>
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}
