import PageWrapper from "../_components/layout/PageWrapper";
import TableSearch from "../_components/table/TableSearch";
import { DataTable } from "../_components/table/data-table";
import Pagination from "../_components/table/Pagination";
import { columns } from "./columns";
import { TableFilter } from "../_components/table/TableFilter";
import ExportCodes from "./_components/ExportCodes";
import CustomDialog from "@/components/shared/CustomDialog";
import { Download } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Suspense } from "react";
import Loading from "../_components/Loading";
import Reactivate from "./_components/Reactivate";
import { getCodes } from "@/lib/server/action/codes";

const breadcrumbItems = [{ label: "Home", href: "/admin" }, { label: "Code" }];

async function SuspendedComponent({
  searchParams,
  params,
}: {
  searchParams: Promise<{ [key: string]: string | undefined }>;
  params: Promise<{ sessionId: string }>;
}) {
  const { sessionId } = await params;
  const { page, search, sortby } = await searchParams;
  const currentPage = page ? +page : 1;
  const { codes, total } = await getCodes({
    sessionId,
    page: currentPage,
    search: search || "",
    sortby: sortby || "",
  });

  return (
    <>
      <DataTable columns={columns} data={codes} />
      <Pagination page={currentPage} count={total} />
    </>
  );
}

const CodePage = ({
  searchParams,
  params,
}: {
  searchParams: Promise<{ [key: string]: string | undefined }>;
  params: Promise<{ sessionId: string }>;
}) => {
  const filterItems = [
    { name: "name", value: "firstName" },
    { name: "Email", value: "email" },
    { name: "Status", value: "status" },
    { name: "School", value: "school" },
  ];

  const RenderButton = () => (
    <CustomDialog
      title="Export"
      trigger={
        <Button size="sm">
          <Download /> Export
        </Button>
      }
    >
      <ExportCodes />
    </CustomDialog>
  );

  return (
    <PageWrapper
      pgTitle="Manage Codes"
      breadcrumbItems={breadcrumbItems}
      headerButton={<RenderButton />}
    >
      {/* TOP */}
      <div className="flex justify-between items-center gap-4 w-full md:w-auto">
        <TableSearch />
        <div className="flex items-center gap-4 self-end">
          <Reactivate />
          <TableFilter filterItems={filterItems} />
        </div>
      </div>
      <Suspense fallback={<Loading />}>
        <SuspendedComponent searchParams={searchParams} params={params} />
      </Suspense>
    </PageWrapper>
  );
};

export default CodePage;
