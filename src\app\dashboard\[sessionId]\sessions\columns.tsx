"use client";

import CustomAlertDialog from "@/components/shared/CustomAlertDialog";
import { CustomDropdown } from "@/components/shared/Dropdown";
import { Button } from "@/components/ui/button";
import {
  DropdownMenuItem,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { ColumnDef } from "@tanstack/react-table";
import { EllipsisVertical } from "lucide-react";
import { toast } from "sonner";
import {
  activateSession,
  deactivateSession,
  deleteSession,
} from "@/lib/server/action/sessions";
import { Session } from "@prisma/client";
import { formatDate } from "@/lib/formatDate";
import { CustomSheet } from "@/components/shared/CustomSheet";
import SessionForm from "../_components/form/SessionForm";

export const columns: ColumnDef<Session>[] = [
  {
    accessorKey: "session.name",
    header: "Name",
    cell: ({ row }) => row.original.name,
  },
  {
    accessorKey: "session.description",
    header: "Description",
    cell: ({ row }) => (
      <div className="w-40 truncate">{row.original.description}</div>
    ),
  },
  {
    accessorKey: "session.isActive",
    header: "Active",
    cell: ({ row }) => (
      <div
        className={`${
          row.original.isActive ? "text-green-500" : "text-red-500"
        }`}
      >
        {row.original.isActive ? "Yes" : "No"}
      </div>
    ),
  },
  {
    accessorKey: "session.startDate",
    header: "Start Date",
    cell: ({ row }) => formatDate(row.original.startDate),
  },
  {
    accessorKey: "session.endDate",
    header: "End Date",
    cell: ({ row }) => formatDate(row.original.endDate),
  },
  {
    id: "action",
    cell: ({ row }) => {
      const session = row.original;

      return (
        <CustomDropdown
          trigger={
            <Button size="icon" variant="ghost">
              <EllipsisVertical />
            </Button>
          }
        >
          <DropdownMenuItem asChild>
            <CustomSheet title="Edit Session" trigger="Edit" asChild={false}>
              <SessionForm
                sessionId={session.id}
                sessionData={{
                  name: session.name,
                  description: session.description || "",
                  startDate: session.startDate,
                  endDate: session.endDate,
                  isActive: session.isActive ? "true" : "false",
                }}
              />
            </CustomSheet>
          </DropdownMenuItem>
          {session.isActive ? (
            <CustomAlertDialog
              asChild={false}
              trigger="Deactivate"
              description="This action will deactivate this session. Do you wish to continue?"
              normal
              onConfirm={async () => {
                const res = await deactivateSession(session.id);
                if (res.success) {
                  toast.success("Session deactivated");
                } else {
                  toast.error("Failed to deactivate session");
                }
              }}
            />
          ) : (
            <CustomAlertDialog
              asChild={false}
              trigger="Activate"
              description="This action will activate this session. Do you wish to continue?"
              normal
              onConfirm={async () => {
                const res = await activateSession(session.id);
                if (res.success) {
                  toast.success("Session activated");
                } else {
                  toast.error("Failed to activate session");
                }
              }}
            />
          )}
          <DropdownMenuSeparator />
          <DropdownMenuItem asChild>
            <CustomAlertDialog
              asChild={false}
              trigger="Delete"
              description={
                session.isActive
                  ? "This is the current active session and this action will delete this session. Do you wish to continue?"
                  : "This action will delete this session. Do you wish to continue?"
              }
              onConfirm={async () => {
                const res = await deleteSession(session.id);
                if (res.success) {
                  toast.success("Session deleted");
                } else {
                  toast.error("Failed to delete session");
                }
              }}
            />
          </DropdownMenuItem>
        </CustomDropdown>
      );
    },
  },
];
