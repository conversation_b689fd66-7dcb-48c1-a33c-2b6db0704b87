import { transporter } from "../mailTransporter";

export async function welcomeMail(email: string, firstName: string) {
  await transporter.sendMail({
    from: process.env.SMTP_USER,
    to: email,
    subject: 'Welcome to CEAP-NCR Virtue Series',
    text: `Dear ${firstName},\n\nThank you for registering with CEAP-NCR Virtue Series! We are pleased to inform you that we have received your registration request.\n\nOur admin team is currently reviewing your submission. You will receive an email notification once your registration has been approved.\n\nIf you have any questions in the meantime, feel free to reach out.\n\nBest regards,\nThe CEAP-NCR Virtue Series Team`,
  });
}

export async function approvedMail({email, firstName, loginCode, link}: {email: string, firstName: string, loginCode: string, link: string}) {
  await transporter.sendMail({
    from: process.env.SMTP_USER,
    to: email,
    subject: 'CEAP-NCR Approval - Your Registration is Complete',
    text: `Dear ${firstName},\n\nCongratulations! Your registration with CEAP-NCR Virtue Series has been successfully approved.\n\nYou can now log in using the following code: ${loginCode}\n\nPlease visit the link below to access your account and complete your login:\n\n${link}\n\nIf you encounter any issues or need assistance, feel free to contact us.\n\nBest regards,\nThe CEAP-NCR Virtue Series Team`,
  });  
}

export async function rejectedMail({email, firstName}: {email: string, firstName: string}) {
  await transporter.sendMail({
    from: process.env.SMTP_USER,
    to: email,
    subject: 'CEAP-NCR Registration Status - Application Rejected',
    text: `Dear ${firstName},\n\nWe regret to inform you that your registration for CEAP-NCR Virtue Series has not been approved at this time.\n\nWe encourage you to review your application and feel free to submit a new request if you wish to try again.\n\nIf you have any questions or need assistance, please don’t hesitate to contact us.\n\nBest regards,\nThe CEAP-NCR Virtue Series Team`,
  });  
}