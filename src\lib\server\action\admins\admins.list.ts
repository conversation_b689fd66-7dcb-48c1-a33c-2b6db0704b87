'use server'

import { appConfig } from "@/config/app";
import prisma from "@/lib/prisma";
import { Prisma } from "@prisma/client";


export type AdminWithUser = Prisma.AdminProfileGetPayload<{
  include: {
    user: true;
  };
}>;

export async function getAdmins({
  page,
  search,
  sortby,
}: {
  page: number;
  search?: string;
  sortby?: string;
}): Promise<{
  admins: AdminWithUser[]; // Corrected type here
  total: number;
}> {
  const whereClause: Prisma.AdminProfileWhereInput = {};
  const orderByClause: Prisma.AdminProfileOrderByWithRelationInput[] = [];

  if (search) {
    whereClause.OR = [
      { user: { firstName: { contains: search } } },
      { user: { lastName: { contains: search } } },
      { user: { email: { contains: search } } },
    ];
  }

  if (sortby) {
    switch (sortby) {
      case "name":
        orderByClause.push({ user: { firstName: "asc" } });
        break;
      case "email":
        orderByClause.push({ user: { email: "asc" } });
        break;
      case "status":
        orderByClause.push({ user: { status: "asc" } });
        break;
      default:
        orderByClause.push({ user: { firstName: "asc" } });
        break;
    }
  } else {
    orderByClause.push({ user: { firstName: "asc" } });
  }

  const queryOptions: Prisma.AdminProfileFindManyArgs = {
    include: {
      user: true,
    },
    where: whereClause,
    orderBy: orderByClause.length > 0 ? orderByClause : [{ user: { firstName: "asc" } }],
    skip: (page - 1) * appConfig.ITEMS_PER_PAGE,
    take: appConfig.ITEMS_PER_PAGE,
  };

  try {
    const admins = await prisma.adminProfile.findMany(queryOptions) as AdminWithUser[];

    const total = await prisma.adminProfile.count({
      where: whereClause,
    });

    return { admins, total };
  } catch (error) {
    console.error("Error fetching admins:", error);
    throw new Error("Failed to fetch admins.");
  }
}

export async function getAdmin(adminId: string) {
  try {
    const admin = await prisma.adminProfile.findUnique({
      where: { id: adminId },
      include: {
        user: {
          select: {
            firstName: true,
            lastName: true,
            email: true,
            phone: true,
            status: true,
            avatar: true,
            role: true,
            createdAt: true,
          }
        },
      },
    });

    return admin;
  } catch (error) {
    console.error("Error fetching admin:", error);
    throw new Error("Failed to fetch admin.");
  }
}