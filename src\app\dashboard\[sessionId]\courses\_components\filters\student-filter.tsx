"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Filter, Search } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";

export default function StudentFilter({
  schools,
}: {
  schools: { label: string; value: string }[];
}) {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedSchool, setSelectedSchool] = useState<string>("all");

  const router = useRouter();

  const handleSearch = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    const params = new URLSearchParams(window.location.search);
    params.set("search", searchTerm);
    router.push(`${window.location.pathname}?${params}`);
  };

  const handleSchoolChange = (value: string) => {
    setSelectedSchool(value);

    const params = new URLSearchParams(window.location.search);
    params.set("grade", value);
    router.push(`${window.location.pathname}?${params}`);
  };

  return (
    <div className="flex flex-col lg:flex-row gap-6">
      <form onSubmit={handleSearch} className="relative flex-1">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
        <Input
          placeholder="Search courses, instructors, or categories..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10"
        />
      </form>
      <div className="flex flex-wrap gap-3 items-center">
        <div className="flex items-center gap-2">
          <Filter className="h-4 w-4 text-gray-500" />
          <span className="text-sm font-medium text-gray-700">Filter by:</span>
        </div>

        <Select value={selectedSchool} onValueChange={handleSchoolChange}>
          <SelectTrigger className="w-32">
            <SelectValue placeholder="Filter by School" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Schools</SelectItem>
            {schools.map((school) => (
              <SelectItem key={school.value} value={school.label}>
                {school.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Button
          variant="outline"
          size="sm"
          onClick={() => {
            setSearchTerm("");
            setSelectedSchool("all");
            router.push(`${window.location.pathname}?`);
          }}
        >
          Clear Filters
        </Button>
      </div>
    </div>
  );
}
