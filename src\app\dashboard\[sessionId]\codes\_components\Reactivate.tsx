"use client";

import CustomAlertDialog from "@/components/shared/CustomAlertDialog";
import { Button } from "@/components/ui/button";
import { reactivateAllCodes } from "@/lib/server/action/codes";
import { RotateCcw } from "lucide-react";
import { toast } from "sonner";

const Reactivate = () => {
  return (
    <CustomAlertDialog
      trigger={
        <Button size="icon" variant="outline">
          <RotateCcw />
        </Button>
      }
      title="Reactivate Codes"
      description="This action will reactivate all expired codes for users who have not yet completed their assessment. Do you wish to continue?"
      normal
      onConfirm={async () => {
        const res = await reactivateAllCodes();
        if (res.success) {
          toast.success(res.message);
        } else {
          toast.error(
            res.message || "Something went wrong, Please try again later."
          );
        }
      }}
    />
  );
};

export default Reactivate;
