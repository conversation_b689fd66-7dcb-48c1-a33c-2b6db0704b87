  import { z } from "zod";

export const meetingSchema = z.object({
  title: z.string().min(1, { message: "Please provide the meeting title" }),
  description: z.string().optional(),
  type: z.enum(["CONFERENCE_CALL", "VIDEO_CALL"]),
  scheduledAt: z.date({ message: "Please provide the scheduled time" }),
  participantEmails: z.string().optional(),
});

export type TMeeting = z.infer<typeof meetingSchema>;
export type TMeetingForm = TMeeting;