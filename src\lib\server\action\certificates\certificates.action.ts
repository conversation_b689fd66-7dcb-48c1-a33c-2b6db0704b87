// 'use server'

// import prisma from "@/lib/prisma"
// import { revalidatePath } from "next/cache";


// export async function getCertificates() {
//   try {
//     const certificates = await prisma.certificates.findMany({
//       include: {
//         student: true,
//         template: true,
//       },
//       orderBy: { issuedAt: 'desc' }
//     });

//     const total = await prisma.certificate.count();

//     return { certificates, total };
//   } catch (error) {
//     console.error("Error fetching certificates:", error);
//     throw new Error("Failed to fetch certificates.");
//   }
// }

// export async function getCertificate(certificateId: string) {
//   try {
//     const certificate = await prisma.certificate.findUnique({
//       where: { id: certificateId },
//       include: {
//         student: true,
//         template: true,
//       },
//     });

//     return certificate;
//   } catch (error) {
//     console.error("Error fetching certificate:", error);
//     throw new Error("Failed to fetch certificate.");
//   }
// }

// export async function createCertificate(data: TGradeForm) {
//   try {
//     await prisma.certificate.create({
//       data: {
//         studentId: data.studentId,
//         templateId: data.templateId,
//         metadata: data.metadata,
//       },
//     })
    
//     revalidatePath('/admin/certificates')

//     return { success: true, message: 'Certificate created' }
//   } catch (error) {
//     console.log(error)
//     return { success: false, error: 'Failed to create certificate' }
//   }
// }

// export async function updateCertificate(certificateId: string, data: TGradeForm) {
//   try {
//     await prisma.certificate.update({
//       where: { id: certificateId },
//       data: {
//         studentId: data.studentId,
//         templateId: data.templateId,
//         metadata: data.metadata,
//       },
//     })

//     revalidatePath('/admin/certificates')

//     return { success: true, message: 'Certificate updated' }
//     } catch (error) {
//     console.log(error)
//     return { success: false, error: 'Failed to update certificate' }
//   }
// }

// export async function deleteCertificate(certificateId: string) {
//   try {
//     await prisma.certificate.delete({
//       where: { id: certificateId }
//     })
    
//     revalidatePath('/admin/certificates')

//     return { success: true }
//   } catch (error) {
//     console.log(error)
//     return { success: false, error: 'Failed to delete certificate' }
//   }
// }
