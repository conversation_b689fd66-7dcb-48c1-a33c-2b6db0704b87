'use server'

import prisma from "@/lib/prisma"
import { ResourceType } from "@prisma/client";
import { revalidatePath } from "next/cache"

type TResource = {
  name: string;
  type: ResourceType;
  url: string;
  content: string;
  fileSize: number;
  mimeType: string;
}

export async function createCourseResourceFolder (courseId: string, data: { name: string }) {
  try {
    await prisma.courseResourceFolder.create({
      data: {
        ...data,
        course: { connect: { id: courseId } }
      }
    })

    revalidatePath(`/admin/courses/${courseId}/files`)
    
    return { success: true, message: 'Folder created' }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to create folder' }
  }
}

export async function updateCourseResourceFolder (folderId: string, data: { name: string }) {
  try {
    await prisma.courseResourceFolder.update({
      where: { id: folderId },
      data
    })

    revalidatePath(`/admin/courses/${folderId}/files`)
    
    return { success: true, message: 'Folder updated' }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to update folder' }
  }
}

export async function deleteCourseResourceFolder (folderId: string) {
  try {
    await prisma.courseResourceFolder.delete({
      where: { id: folderId }
    })

    revalidatePath(`/admin/courses/${folderId}/files`)
    
    return { success: true, message: 'Folder deleted' }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to delete folder' }
  }
}

export async function createCourseResource (courseId: string, data: TResource, folderId?: string) {
  try {
    await prisma.courseResource.create({
      data: {
        ...data,
        course: { connect: { id: courseId } },
        folder: folderId ? { connect: { id: folderId } } : undefined
      }
    })

    revalidatePath(`/admin/courses/${courseId}/files`)
    
    return { success: true, message: 'Resource created' }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to create resource' }
  }
}

export async function updateCourseResource (resourceId: string, data: Partial<TResource>) {
  try {
    await prisma.courseResource.update({
      where: { id: resourceId },
      data
    })

    revalidatePath(`/admin/courses/${resourceId}/files`)
    
    return { success: true, message: 'Resource updated' }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to update resource' }
  }
}

export async function deleteCourseResource (resourceId: string) {
  try {
    await prisma.courseResource.delete({
      where: { id: resourceId }
    })

    revalidatePath(`/admin/courses/${resourceId}/files`)
    
    return { success: true, message: 'Resource deleted' }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to delete resource' }
  }
}