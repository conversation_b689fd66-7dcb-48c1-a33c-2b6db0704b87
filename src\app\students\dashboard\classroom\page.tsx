import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Search, ExternalLink } from "lucide-react";

export default function VirtualClassroom() {
  const sessions = [
    {
      id: 1,
      type: "Conference call",
      status: "Ongoing",
      date: "June 10, 2025 10:00 AM",
      description:
        "This conference call is for student who will participate into the incoming activities",
      icon: "📞",
      color: "blue",
    },
    {
      id: 2,
      type: "Video call",
      status: "Upcoming",
      date: "June 11, 2025 10:00 AM",
      description:
        "This conference call is for student who will participate into the incoming activities",
      icon: "📹",
      color: "green",
    },
    {
      id: 3,
      type: "Video call",
      status: "Upcoming",
      date: "June 11, 2025 10:00 AM",
      description:
        "This conference call is for student who will participate into the incoming activities",
      icon: "📹",
      color: "green",
    },
    {
      id: 4,
      type: "Video call",
      status: "Upcoming",
      date: "June 11, 2025 10:00 AM",
      description:
        "This conference call is for student who will participate into the incoming activities",
      icon: "📞",
      color: "blue",
    },
    {
      id: 5,
      type: "Video call",
      status: "Upcoming",
      date: "June 11, 2025 10:00 AM",
      description:
        "This conference call is for student who will participate into the incoming activities",
      icon: "📹",
      color: "green",
    },
    {
      id: 6,
      type: "Video call",
      status: "Upcoming",
      date: "June 11, 2025 10:00 AM",
      description:
        "This conference call is for student who will participate into the incoming activities",
      icon: "📞",
      color: "blue",
    },
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Virtual Classroom</h1>
        <div className="relative w-64">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
          <Input placeholder="Search" className="pl-10" />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
        {sessions.map((session) => (
          <Card key={session.id} className="py-0">
            <CardContent className="p-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Badge
                    variant={
                      session.status === "Ongoing" ? "default" : "secondary"
                    }
                  >
                    {session.status}
                  </Badge>
                  <span className="text-sm text-muted-foreground">
                    {session.date}
                  </span>
                </div>

                <div className="flex items-center gap-3">
                  <h3 className="font-semibold text-lg">{session.type}</h3>
                  <div
                    className={`w-8 h-8 rounded-full flex items-center justify-center ${
                      session.color === "blue" ? "bg-blue-600" : "bg-green-600"
                    }`}
                  >
                    <span className="text-white text-lg">{session.icon}</span>
                  </div>
                </div>

                <p className="text-sm text-muted-foreground">
                  {session.description}
                </p>

                <div className="flex gap-2">
                  {session.status === "Ongoing" ? (
                    <>
                      <Button variant="outline" size="sm" className="flex-1">
                        <ExternalLink className="w-4 h-4 mr-2" />
                        View
                      </Button>
                      <Button size="sm" className="flex-1">
                        Join
                      </Button>
                    </>
                  ) : (
                    <Button variant="outline" size="sm" className="w-full">
                      <ExternalLink className="w-4 h-4 mr-2" />
                      View
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
