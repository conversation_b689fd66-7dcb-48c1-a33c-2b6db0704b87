import { transporter } from "../mailTransporter";

export async function certificateMail({email, firstName, loginCode}: {email: string, firstName: string, loginCode: string}) {
  await transporter.sendMail({
    from: process.env.SMTP_USER,
    to: email,
    subject: 'CEAP-NCR Virtue Series - Assessment Passed',
    text: `Dear ${firstName},\n\nCongratulations! You have successfully passed the assessment. You can now proceed to the training camp.\n\nHere is your login code: ${loginCode}\n\nIf you have any questions or need further assistance, feel free to reach out.\n\nBest regards,\nThe CEAP-NCR Virtue Series Team`,
  });
}