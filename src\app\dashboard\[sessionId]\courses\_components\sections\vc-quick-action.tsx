"use client";

import { CustomSheet } from "@/components/shared/CustomSheet";
import { Card, CardContent } from "@/components/ui/card";
import { Plus, Users, Video } from "lucide-react";
import React from "react";
import CreateMeetingForm from "../forms/create-meeting-form";
import { instantMeeting } from "@/lib/server/action/courses/virtual-classrooms";
import { toast } from "sonner";

export default function VCQuickAction({ courseId }: { courseId: string }) {
  const handleStartInstantMeeting = async () => {
    const res = await instantMeeting(courseId);
    if (res.success) {
      window.open(res.meetUrl!, "_blank");
    } else {
      toast.error(res.error);
    }
  };
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-8">
      <Card
        className="hover:shadow-md transition-shadow cursor-pointer py-0"
        onClick={handleStartInstantMeeting}
      >
        <CardContent className="p-6 text-center">
          <Video className="w-8 h-8 mx-auto mb-3 text-primary" />
          <h3 className="font-semibold mb-2">Start Instant Meeting</h3>
          <p className="text-sm text-gray-600">Begin a meeting right now</p>
        </CardContent>
      </Card>

      <CustomSheet
        title="Create Meeting"
        trigger={
          <Card className="hover:shadow-md transition-shadow cursor-pointer py-0">
            <CardContent className="p-6 text-center">
              <Plus className="w-8 h-8 mx-auto mb-3 text-green-600" />
              <h3 className="font-semibold mb-2">Schedule Meeting</h3>
              <p className="text-sm text-gray-600">Plan a meeting for later</p>
            </CardContent>
          </Card>
        }
      >
        <CreateMeetingForm courseId={courseId} />
      </CustomSheet>

      <Card
        className="hover:shadow-md transition-shadow cursor-pointer py-0"
        onClick={() => window.open("https://meet.google.com/landing", "_blank")}
      >
        <CardContent className="p-6 text-center">
          <Users className="w-8 h-8 mx-auto mb-3 text-purple-600" />
          <h3 className="font-semibold mb-2">Join Meeting</h3>
          <p className="text-sm text-gray-600">Enter a meeting ID</p>
        </CardContent>
      </Card>
    </div>
  );
}
