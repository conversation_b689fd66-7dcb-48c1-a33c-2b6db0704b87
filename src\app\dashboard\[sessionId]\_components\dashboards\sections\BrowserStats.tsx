import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { ArrowDown, ArrowUp } from "lucide-react";

interface BrowserStatsProps {
  className?: string;
  title: string;
  type?: "browser" | "channel";
}

export function BrowserStats({
  className,
  title,
  type = "browser",
}: BrowserStatsProps) {
  const browserData = [
    {
      name: "Chrome",
      sessions: "10853",
      sessionsPercent: "52%",
      bounceRate: "52.80%",
      transactions: "566",
      transactionsPercent: "92%",
    },
    {
      name: "Microsoft Edge",
      sessions: "2545",
      sessionsPercent: "47%",
      bounceRate: "47.54%",
      transactions: "498",
      transactionsPercent: "81%",
    },
    {
      name: "Internet-Explorer",
      sessions: "1836",
      sessionsPercent: "38%",
      bounceRate: "41.12%",
      transactions: "455",
      transactionsPercent: "74%",
    },
  ];

  const channelData = [
    {
      name: "Organic search",
      sessions: "10853",
      sessionsPercent: "52%",
      prevPeriod: "566",
      prevPeriodPercent: "92%",
      change: "52.80%",
      isPositive: true,
    },
    {
      name: "Direct",
      sessions: "2545",
      sessionsPercent: "47%",
      prevPeriod: "498",
      prevPeriodPercent: "81%",
      change: "17.20%",
      isPositive: false,
    },
    {
      name: "Referral",
      sessions: "1836",
      sessionsPercent: "38%",
      prevPeriod: "455",
      prevPeriodPercent: "74%",
      change: "41.12%",
      isPositive: true,
    },
  ];

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>
                {type === "browser" ? "Browser" : "Channel"}
              </TableHead>
              <TableHead>Sessions</TableHead>
              {type === "browser" ? (
                <>
                  <TableHead>Bounce Rate</TableHead>
                  <TableHead>Transactions</TableHead>
                </>
              ) : (
                <>
                  <TableHead>Prev. Period</TableHead>
                  <TableHead>% Change</TableHead>
                </>
              )}
            </TableRow>
          </TableHeader>
          <TableBody>
            {type === "browser"
              ? browserData.map((item) => (
                  <TableRow key={item.name}>
                    <TableCell className="font-medium">{item.name}</TableCell>
                    <TableCell>
                      {item.sessions}{" "}
                      <span className="text-muted-foreground">
                        ({item.sessionsPercent})
                      </span>
                    </TableCell>
                    <TableCell>{item.bounceRate}</TableCell>
                    <TableCell>
                      {item.transactions}{" "}
                      <span className="text-muted-foreground">
                        ({item.transactionsPercent})
                      </span>
                    </TableCell>
                  </TableRow>
                ))
              : channelData.map((item) => (
                  <TableRow key={item.name}>
                    <TableCell className="font-medium">
                      <span className="text-green-500">{item.name}</span>
                    </TableCell>
                    <TableCell>
                      {item.sessions}{" "}
                      <span className="text-muted-foreground">
                        ({item.sessionsPercent})
                      </span>
                    </TableCell>
                    <TableCell>
                      {item.prevPeriod}{" "}
                      <span className="text-muted-foreground">
                        ({item.prevPeriodPercent})
                      </span>
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant={item.isPositive ? "default" : "destructive"}
                        className="gap-1"
                      >
                        {item.change}{" "}
                        {item.isPositive ? (
                          <ArrowUp className="h-3 w-3" />
                        ) : (
                          <ArrowDown className="h-3 w-3" />
                        )}
                      </Badge>
                    </TableCell>
                  </TableRow>
                ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}
