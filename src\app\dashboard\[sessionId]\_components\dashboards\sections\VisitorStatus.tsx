import { Card, CardContent } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";

interface VisitorStatsProps {
  className?: string;
}

export function VisitorStats({ className }: VisitorStatsProps) {
  const days = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];

  return (
    <Card className={className}>
      <CardContent className="p-6">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">
                New Visitors
              </p>
              <h2 className="text-3xl font-bold">1,282</h2>
              <p className="text-xs text-muted-foreground">Logged Visitors</p>
            </div>
            <div className="flex -space-x-2">
              {[1, 2, 3, 4].map((i) => (
                <Avatar key={i} className="border-2 border-background">
                  <AvatarImage
                    src={`/placeholder.svg?height=32&width=32&index=${i}`}
                  />
                  <AvatarFallback>U{i}</AvatarFallback>
                </Avatar>
              ))}
              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-xs text-primary-foreground">
                +6
              </div>
            </div>
          </div>

          <div className="mt-6">
            <div className="flex items-end justify-between">
              {days.map((day) => (
                <div key={day} className="flex flex-col items-center">
                  <div
                    className="w-8 bg-muted rounded-t-md"
                    style={{
                      height: `${Math.random() * 60 + 20}px`,
                    }}
                  />
                  <span className="mt-2 text-xs">{day}</span>
                </div>
              ))}
            </div>
          </div>

          <Button
            variant="default"
            className="mt-4 w-full bg-green-500 hover:bg-green-600"
          >
            More Detail <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
