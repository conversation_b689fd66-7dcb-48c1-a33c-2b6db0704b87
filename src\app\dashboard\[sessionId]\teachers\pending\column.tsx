"use client";

import CustomAlertDialog from "@/components/shared/CustomAlertDialog";
import CustomDialog from "@/components/shared/CustomDialog";
import { CustomDropdown } from "@/components/shared/Dropdown";
import { Button } from "@/components/ui/button";
import {
  DropdownMenuItem,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import {
  approveTeacher,
  rejectTeacher,
  TeacherWithUserAndSchool,
} from "@/lib/server/action/teachers";
import { ColumnDef } from "@tanstack/react-table";
import { EllipsisVertical } from "lucide-react";
import { toast } from "sonner";
import ViewTeacher from "../../_components/view/ViewTeacher";
import { Avatar, AvatarImage } from "@/components/ui/avatar";

export const columns: ColumnDef<TeacherWithUserAndSchool>[] = [
  {
    accessorKey: "name",
    header: () => <div className="ml-2">Name</div>,
    cell: ({ row }) => {
      const image = row.original.user.avatar as string;
      return (
        <div className="ml-2 flex gap-3 items-center">
          <Avatar>
            <AvatarImage src={image ?? "/images/avatar.jpg"} alt="user photo" />
            {/* <AvatarFallback>{}</AvatarFallback> */}
          </Avatar>
          <div className="font-medium truncate">{`${row.original.user.firstName} ${row.original.user.lastName}`}</div>
        </div>
      );
    },
  },
  {
    accessorKey: "user.email",
    header: "Email",
    cell: ({ row }) => row.original.user.email,
  },
  {
    accessorKey: "user.phone",
    header: "Phone",
    cell: ({ row }) => row.original.user.phone,
  },
  {
    accessorKey: "school.name",
    header: "School",
    cell: ({ row }) => row.original.school.name,
  },
  {
    accessorKey: "user.status",
    header: "Status",
    cell: ({ row }) => (
      <div
        className={`capitalize ${
          row.original.user.status === "APPROVED"
            ? "text-green-600"
            : row.original.user.status === "PENDING"
            ? "text-amber-600"
            : "text-red-600"
        }`}
      >
        {row.original.user.status.toLowerCase()}
      </div>
    ),
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const teacher = row.original;

      return (
        <CustomDropdown
          trigger={
            <Button size="icon" variant="ghost">
              <EllipsisVertical />
            </Button>
          }
        >
          <DropdownMenuItem asChild>
            <CustomDialog
              title="Approve Teacher"
              description={`Do you want to approve ${teacher.user.firstName} ${teacher.user.lastName}?`}
              asChild={false}
              trigger="Approve"
              footer
              onConfirm={async () => {
                const res = await approveTeacher(teacher.id);
                if (res.success) {
                  toast.success(res.message);
                } else {
                  toast.error(res.message);
                }
              }}
            />
          </DropdownMenuItem>
          <DropdownMenuItem asChild>
            <CustomDialog asChild={false} trigger="View" title="User Details">
              <ViewTeacher
                userPhoto={teacher.user.avatar}
                firstName={teacher.user.firstName}
                lastName={teacher.user.lastName}
                status={teacher.user.status}
                email={teacher.user.email}
                school={teacher.school.name}
                course={teacher.courseAssignments[0]?.course.title || ""}
              />
            </CustomDialog>
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem asChild>
            <CustomAlertDialog
              asChild={false}
              trigger="Reject"
              onConfirm={async () => {
                const res = await rejectTeacher(teacher.id);
                toast(res.message);
              }}
            />
          </DropdownMenuItem>
        </CustomDropdown>
      );
    },
  },
];
