"use client";

import React from "react";
// import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import { Plus } from "lucide-react";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { User } from "next-auth";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

const DashboardHeader = ({ user }: { user: User }) => {
  const [searchQuery, setSearchQuery] = React.useState("");

  return (
    <div className="flex h-16 items-center gap-4 border-b px-4 sticky top-0 z-50 bg-background">
      <SidebarTrigger />
      <div className="flex-1">
        <Input
          placeholder="Search..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="max-w-sm"
        />
      </div>
      <Button variant="outline" size="icon">
        <Plus className="h-5 w-5" />
        <span className="sr-only">Add new</span>
      </Button>

      <Button size="lg" variant="outline" className="px-1 sm:px-2">
        <Avatar className="h-8 w-8 rounded-lg">
          <AvatarImage
            src={user.avatar ?? "/images/avatar.jpg"}
            alt="profile picture"
          />
          <AvatarFallback className="rounded-lg">
            {user.firstName[0].toLocaleUpperCase() +
              user.lastName[0].toLocaleUpperCase()}
          </AvatarFallback>
        </Avatar>
        <div className="hidden sm:grid flex-1 text-left text-sm leading-tight">
          <span className="truncate font-semibold">
            {user.firstName + " " + user.lastName}
          </span>
          <span className="truncate text-xs">{user.email}</span>
        </div>
      </Button>
    </div>
  );
};

export default DashboardHeader;
