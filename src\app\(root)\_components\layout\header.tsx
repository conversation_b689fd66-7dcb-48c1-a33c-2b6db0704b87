import Logo from "@/components/shared/logo";
import { Button } from "@/components/ui/button";
import { User } from "next-auth";
import Link from "next/link";

export default function Header({ user }: { user?: User }) {
  return (
    <header className="w-full border-b bg-white">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <Logo />
          <div className="flex items-center gap-3">
            {user ? (
              <Button asChild>
                <Link
                  href={`${
                    user.role === "ADMIN" ? "/dashboard" : "/students/dashboard"
                  }`}
                >
                  Dashboard
                </Link>
              </Button>
            ) : (
              <>
                <Button asChild variant="outline">
                  <Link href="/sign-in?type=lc">Login</Link>
                </Button>

                <Button>Sign Up</Button>
              </>
            )}
          </div>
        </div>
      </div>
    </header>
  );
}
