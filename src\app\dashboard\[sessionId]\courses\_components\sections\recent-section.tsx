import { Card, CardContent } from "@/components/ui/card";
import { getTimeLeft } from "@/lib/timeLeft";
import { CourseResource } from "@prisma/client";

export default function RecentSection({
  recentFiles,
}: {
  recentFiles: CourseResource[];
}) {
  return (
    <div>
      <h3 className="text-lg font-semibold mb-4">Recent files</h3>
      {recentFiles.length === 0 ? (
        <p className="text-sm text-muted-foreground">No recent files found.</p>
      ) : (
        <div className="space-y-2">
          {recentFiles.map((file) => (
            <Card
              key={file.id}
              className="hover:shadow-md transition-shadow cursor-pointer py-0"
            >
              <CardContent className="p-4">
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <div className="w-8 h-10 bg-primary rounded-sm flex items-center justify-center">
                      <span className="text-white text-xs font-bold">W</span>
                    </div>
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium">{file.name}</h4>
                    <p className="text-sm text-gray-500">{file.type}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-gray-500">{file.fileSize}</p>
                    <p className="text-xs text-gray-400">
                      {getTimeLeft(file.createdAt)}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
