import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";
import { Fragment } from "react";
import FolderCard from "../_components/cards/FolderCard";
import RecentFilesSection from "../_components/sections/recent-file";

export default async function FileManagementPage() {
  // const [folders, resources] = await Promise.all([
  //   getCourseResourceFolders(courseId),
  //   getCourseResources(courseId),
  // ]);

  const folders = [
    {
      id: "1",
      name: "Folder 1",
      resources: [
        {
          id: "1",
          title: "File 1",
          type: "PDF",
          size: "2.5 MB",
          downloadUrl: "#",
        },
        {
          id: "2",
          title: "File 2",
          type: "PDF",
          size: "1.8 MB",
          downloadUrl: "#",
        },
        {
          id: "3",
          title: "File 3",
          type: "PDF",
          size: "1.8 MB",
          downloadUrl: "#",
        },
      ],
    },
    {
      id: "2",
      name: "Folder 2",
      resources: [
        {
          id: "4",
          title: "File 4",
          type: "PDF",
          size: "1.8 MB",
          downloadUrl: "#",
        },
        {
          id: "5",
          title: "File 5",
          type: "PDF",
          size: "1.8 MB",
          downloadUrl: "#",
        },
        {
          id: "6",
          title: "File 6",
          type: "PDF",
          size: "1.8 MB",
          downloadUrl: "#",
        },
      ],
    },
  ];

  const resources = [
    {
      id: "1",
      name: "File 1",
      type: "DOCX",
      fileSize: "2.5 MB",
      downloadUrl: "#",
      viewedAt: new Date(),
    },
    {
      id: "2",
      name: "File 2",
      type: "XLSX",
      fileSize: "1.8 MB",
      downloadUrl: "#",
      viewedAt: new Date(),
    },
    {
      id: "3",
      name: "File 3",
      type: "PDF",
      fileSize: "1.8 MB",
      downloadUrl: "#",
      viewedAt: new Date(),
    },
  ];

  return (
    <div className="space-y-4 max-w-7xl mx-auto">
      <div className="flex lg:items-center justify-between flex-col lg:flex-row gap-3">
        <h2 className="text-2xl font-bold">File Management</h2>
        <div className="flex items-center gap-4">
          <div className="relative flex w-full">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input placeholder="Search files" className="pl-10 w-64 flex-1" />
          </div>
        </div>
      </div>

      <div className="space-y-6">
        <div className="space-y-2 mb-4">
          <h3 className="text-lg font-semibold">Folders</h3>
          {folders.length === 0 ? (
            <p className="text-sm text-muted-foreground">
              No folders found. Create a new folder.
            </p>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {folders.map((folder) => (
                <Fragment key={folder.id}>
                  <FolderCard folder={folder} />
                </Fragment>
              ))}
            </div>
          )}
        </div>
        <RecentFilesSection recentFiles={resources} />
      </div>
    </div>
  );
}
