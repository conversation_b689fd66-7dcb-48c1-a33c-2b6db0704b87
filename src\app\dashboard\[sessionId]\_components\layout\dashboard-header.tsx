"use client";

import React from "react";
// import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import { Plus } from "lucide-react";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import UserDropdown from "@/components/shared/layout.tsx/user-dropdown";
import { User } from "next-auth";

const DashboardHeader = ({
  activeSessionId,
  user,
}: {
  activeSessionId: string;
  user: User;
}) => {
  const [searchQuery, setSearchQuery] = React.useState("");

  return (
    <div className="flex h-16 items-center gap-4 border-b px-4 sticky top-0 z-50 bg-background">
      <SidebarTrigger />
      <div className="flex-1">
        <Input
          placeholder="Search..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="max-w-sm"
        />
      </div>
      <Button variant="outline" size="icon">
        <Plus className="h-5 w-5" />
        <span className="sr-only">Add new</span>
      </Button>

      <UserDropdown
        activeSessionId={activeSessionId}
        user={{
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          avatar: user.avatar,
        }}
      />
      {/* <Avatar>
        <AvatarImage src="/placeholder.svg" />
        <AvatarFallback>CN</AvatarFallback>
      </Avatar> */}
    </div>
  );
};

export default DashboardHeader;
