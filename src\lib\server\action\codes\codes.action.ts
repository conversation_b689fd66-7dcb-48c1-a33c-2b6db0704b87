'use server'

import prisma from "@/lib/prisma";
import { TCode } from "./codes.schema";


// export async function createCode (userId: string, schoolId: string) {
//   try {
//     const existingCode = await prisma.code.findUnique({
//       where: { userId },
//     });

//     if (existingCode) {
//       return { success: false, error: "Code already exists" };
//     }

//     const loginCode = uuidv4().toString().slice(0, 6);

//     const data = {
//       code: loginCode,
//       expireTime: new Date(Date.now() + 1000 * 60 * 60 * 24 * 30),
//     };

//     const code = await prisma.code.create({
//       data: {
//         ...data,
//         user: { connect: { id: userId } },
//         school: { connect: { id: schoolId } },
//       }
//     });

//     return { success: true, code };
//   } catch (error) {
//     console.error("Error creating code:", error);
//     return { success: false, error: "Failed to create code" };
//   }
// };

export async function updateCode (codeId: string, data: Pick<TCode, 'status' | 'expireTime'>) {
  try {
    const code = await prisma.code.update({
      where: { id: codeId },
      data: {
        status: data.status,
        expireTime: data.expireTime,
      },
    });

    return { success: true, code };
  } catch (error) {
    console.error("Error updating code:", error);
    return { success: false, error: "Failed to update code" };
  }
};

export const deleteCode = async (codeId: string) => {
  try {
    await prisma.code.delete({
      where: { id: codeId },
    });

    return { success: true };
  } catch (error) {
    console.error("Error deleting code:", error);
    return { success: false, error: "Failed to delete code" };
  }
};

export async function reactivateAllCodes() {
  try {
    await prisma.code.updateMany({
      where: {
        status: "EXPIRED"
      },
      data: {
        status: "ACTIVE",
        expireTime: new Date(Date.now() + 1000 * 60 * 60 * 24 * 30),
      },
    });

    return { success: true, message: "Codes reactivated" };
  } catch (error) {
    console.error("Error reactivating codes:", error);
    return { success: false, error: "Failed to reactivate codes" };
  }
}

export async function reactivateCode (codeId: string) {
  try {
    const code = await prisma.code.update({
      where: { id: codeId },
      data: {
        status: "ACTIVE",
        expireTime: new Date(Date.now() + 1000 * 60 * 60 * 24 * 30),
      },
    });

    return { success: true, code };
  } catch (error) {
    console.error("Error reactivating code:", error);
    return { success: false, error: "Failed to reactivate code" };
  }
}

export async function deactivateCode (codeId: string) {
  try {
    const code = await prisma.code.update({
      where: { id: codeId },
      data: {
        status: "INACTIVE",
      },
    });

    return { success: true, code };
  } catch (error) {
    console.error("Error deactivating code:", error);
    return { success: false, error: "Failed to deactivate code" };
  }
}