/*
  Warnings:

  - You are about to alter the column `duration` on the `lessons` table. The data in that column could be lost. The data in that column will be cast from `VarChar(191)` to `Int`.
  - A unique constraint covering the columns `[docId]` on the table `lessons` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[videoId]` on the table `lessons` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE `lessons` ADD COLUMN `docId` VARCHAR(191) NULL,
    ADD COLUMN `videoId` VARCHAR(191) NULL,
    MODIFY `duration` INTEGER NULL;

-- CreateIndex
CREATE UNIQUE INDEX `lessons_docId_key` ON `lessons`(`docId`);

-- CreateIndex
CREATE UNIQUE INDEX `lessons_videoId_key` ON `lessons`(`videoId`);
