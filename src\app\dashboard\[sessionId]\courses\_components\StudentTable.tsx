"use client";

import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Eye, X } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { getCourseStudents } from "@/lib/server/action/courses";
import AcademicReport from "@/components/shared/AcademicReport";
import { Suspense, useState } from "react";
import Loading from "../../_components/Loading";

export default function StudentTable({
  courseStudents,
}: {
  courseStudents: Awaited<ReturnType<typeof getCourseStudents>>;
}) {
  const [open, setOpen] = useState(false);

  return (
    <div className="bg-white rounded-lg border overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Name</TableHead>
            <TableHead>Email</TableHead>
            <TableHead>School</TableHead>
            <TableHead>Module Progress</TableHead>
            <TableHead>Action</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {courseStudents.total === 0 ? (
            <TableRow>
              <TableCell colSpan={5} className="h-24 text-center">
                No students found.
              </TableCell>
            </TableRow>
          ) : (
            <>
              {courseStudents.students.map((student) => {
                const enrollment = student.student.enrollment!;

                return (
                  <TableRow key={student.id}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <Avatar className="w-8 h-8">
                          <AvatarImage src={"/images/placeholder.svg"} />
                          <AvatarFallback>
                            {student.student.user.firstName.charAt(0)}
                          </AvatarFallback>
                        </Avatar>
                        <span className="font-medium">
                          {student.student.user.firstName}{" "}
                          {student.student.user.lastName}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell className="text-gray-600">
                      {student.student.user.email}
                    </TableCell>
                    <TableCell className="text-gray-600">
                      {student.student.school.name}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        {enrollment.completedAt ? (
                          <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
                            Passed
                          </Badge>
                        ) : (
                          <div className="flex items-center gap-2">
                            <Progress
                              value={enrollment.progress || 0}
                              className="w-20"
                            />
                            <span className="text-sm text-gray-600">
                              {enrollment.progress}%
                            </span>
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setOpen(true)}
                      >
                        <Eye className="w-4 h-4" />
                      </Button>
                      {open ? (
                        <div className="fixed inset-0 bg-black/50 flex justify-center items-center z-50">
                          <div className="bg-white p-6 max-h-[70%] rounded-lg max-w-7xl overflow-y-auto no-scrollbar relative">
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => setOpen(false)}
                              className="absolute top-2 right-2"
                            >
                              <X />
                            </Button>
                            <h2 className="mb-4 text-lg font-semibold">
                              <span className="text-muted-foreground">
                                Student -
                              </span>{" "}
                              {` ${student.student.user.firstName} ${student.student.user.lastName}`}
                            </h2>
                            <Suspense fallback={<Loading />}>
                              <AcademicReport studentId={student.student.id} />
                            </Suspense>
                          </div>
                        </div>
                      ) : null}
                    </TableCell>
                  </TableRow>
                );
              })}
            </>
          )}
        </TableBody>
      </Table>
    </div>
  );
}
