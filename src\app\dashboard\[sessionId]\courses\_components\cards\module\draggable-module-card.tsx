"use client";

// import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Edit, Eye, GripVertical, Trash2 } from "lucide-react";
import {
  deleteModule,
  ModulesWithDetails,
} from "@/lib/server/action/courses/modules";
import { toast } from "sonner";
import CustomAlertDialog from "@/components/shared/CustomAlertDialog";
import { CustomSheet } from "@/components/shared/CustomSheet";
import ModuleForm from "../../forms/ModuleForm";

export default function DraggableModuleCard({
  courseId,
  sessionId,
  modules,
}: {
  courseId: string;
  sessionId: string;
  modules: ModulesWithDetails[];
}) {
  // const getStatusColor = (status: string) => {
  //   switch (status) {
  //     case "Published":
  //       return "bg-green-100 text-green-800";
  //     case "Draft":
  //       return "bg-yellow-100 text-yellow-800";
  //     case "Archived":
  //       return "bg-gray-100 text-gray-800";
  //     default:
  //       return "bg-gray-100 text-gray-800";
  //   }
  // };

  return (
    <div className="space-y-4">
      {modules.map((module) => (
        <Card
          key={module.id}
          className="relative hover:shadow-md transition-shadow py-4"
        >
          <CardContent>
            <div className="absolute top-1 right-1 z-10">
              <Button size="icon" variant="ghost" className="cursor-grab">
                <GripVertical />
              </Button>
            </div>
            <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-2">
                  <h3 className="text-lg font-semibold">{module.title}</h3>
                  {/* <Badge className={getStatusColor(module.status)}>
                    {module.status}
                  </Badge> */}
                </div>
                <p className="text-gray-600 text-sm mb-3">
                  {module.description}
                </p>
                <div className="flex flex-wrap gap-4 text-sm text-gray-500">
                  <span>{module.questions} questions</span>
                  {/* <span>{module.timeLimit} minutes</span> */}
                  <span>{module.totalPoints} points</span>
                  <span>{module.attempts} attempts</span>
                  <span>Created: {module.created}</span>
                </div>
              </div>

              <div className="flex flex-wrap gap-2">
                <Button variant="outline" size="sm" asChild>
                  <Link
                    href={`/dashboard/${sessionId}/courses/${courseId}/modules/${module.id}`}
                  >
                    <Eye className="h-4 w-4 mr-1" />
                    View
                  </Link>
                </Button>
                <CustomSheet
                  title="Edit Module"
                  trigger={
                    <Button variant="outline" size="sm">
                      <Edit className="h-4 w-4 mr-1" />
                      Edit
                    </Button>
                  }
                >
                  <ModuleForm
                    courseId={courseId}
                    moduleId={module.id}
                    moduleData={{
                      title: module.title,
                      description: module.description || "",
                    }}
                  />
                </CustomSheet>
                <CustomAlertDialog
                  trigger={
                    <Button
                      variant="outline"
                      size="sm"
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4 mr-1" />
                      Delete
                    </Button>
                  }
                  title="Delete Module"
                  description="Are you sure you want to delete this module?"
                  onConfirm={async () => {
                    const res = await deleteModule(module.id);
                    if (res.success) {
                      toast.success("Module deleted");
                    } else {
                      toast.error("Failed to delete module");
                    }
                  }}
                />
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
