"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { Form } from "@/components/ui/form";
import { Button } from "../../../../../../components/ui/button";
import { Loader } from "lucide-react";
import { toast } from "sonner";
import { FormInputField } from "../../../../../../components/form-element/input-field";
import { FormTextareaField } from "../../../../../../components/form-element/text-area";
import { FormComboboxField } from "../../../../../../components/form-element/select-search";
import {
  courseSchema,
  TCourseForm,
} from "@/lib/server/action/courses/course.schema";
import { useEffect, useState } from "react";
import { getGradeOptions } from "@/lib/server/action/grades";
import { getTeacherOptions } from "@/lib/server/action/teachers";
import { updateCourse } from "@/lib/server/action/courses";

export default function CourseForm({
  sessionId,
  courseId,
  courseData,
}: {
  sessionId: string;
  courseId: string;
  courseData: TCourseForm;
}) {
  const [grades, setGrades] = useState<{ label: string; value: string }[]>([]);
  const [teachers, setTeachers] = useState<{ label: string; value: string }[]>(
    []
  );

  useEffect(() => {
    async function gradeOptions() {
      const res = await getGradeOptions(sessionId);
      setGrades(res);
    }
    async function teacherOptions() {
      const res = await getTeacherOptions(sessionId);
      setTeachers(
        res.map((teacher) => ({
          label: teacher.name,
          value: teacher.id,
        }))
      );
    }
    gradeOptions();
    teacherOptions();
  }, [sessionId]);

  const form = useForm<TCourseForm>({
    resolver: zodResolver(courseSchema),
    defaultValues: courseData,
    mode: "onChange",
  });

  const onSubmit = async (values: TCourseForm) => {
    const res = await updateCourse(courseId, values);
    if (res.success) {
      toast.success(res.message);
    } else {
      toast.error(res.error);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <div className="grid gap-6 md:grid-cols-2">
          <FormInputField
            control={form.control}
            name="title"
            label="Title"
            placeholder="Enter course title"
          />
          <FormComboboxField
            control={form.control}
            name="gradeId"
            label="Grade"
            placeholder="Select a grade"
            options={grades}
          />
          <FormComboboxField
            control={form.control}
            name="teacherId"
            label="Teacher"
            placeholder="Select a teacher"
            options={teachers}
          />
        </div>
        <FormTextareaField
          control={form.control}
          name="description"
          label="Description"
          placeholder="Enter course description"
        />
        <div className="flex justify-end gap-2">
          <Button type="submit" disabled={form.formState.isSubmitting}>
            {form.formState.isSubmitting ? (
              <>
                <Loader /> Updating Course
              </>
            ) : (
              <>Update Course</>
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}
