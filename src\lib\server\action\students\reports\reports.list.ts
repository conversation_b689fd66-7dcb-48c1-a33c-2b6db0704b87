'use server'

import prisma from "@/lib/prisma"

export async function getStudentReport(studentId: string) {
  try {
    const enrollment = await prisma.enrollment.findUnique({
      where: { studentId },
      select: {
        progress: true,
        completedAt: true,
        course: {
          select: {
            id: true,
            title: true,
          }
        }
      }
    })
    
    if (!enrollment) return null

    const [ modulesData, activitiesData ] = await Promise.all([prisma.module.findMany({
      where: { courseId: enrollment.course.id },
      select: {
        id: true,
        title: true,
        assessment: {
          select: {
            _count: {
              select: {
                attempts: true
              }
            },
            attempts: {
              where: { studentId },
              select: {
                score: true
              },
              orderBy: [
                { score: 'desc' },
                { attemptNumber: 'desc' }
              ],
              take: 1
            }
          }
        }
      }
    }),
      prisma.activity.findMany({
        where: { courseId: enrollment.course.id },
        select: {
          id: true,
          question: true,
          responses: {
            where: { studentId },
            select: {
              score: true
            },
            orderBy: { submittedAt: 'desc' },
            take: 1
          }
        }
      })
    ])    
    
    return {
      courseName: enrollment.course.title,
      modules: modulesData.map(module => ({
        name: module.title,
        attempts: module.assessment?._count.attempts ?? 0,
        score: module.assessment?.attempts?.[0]?.score ?? 0
      })),
      totalModuleScore: modulesData.reduce((sum, module) => sum + (module.assessment?.attempts?.[0]?.score ?? 0), 0),
      achievementTest: activitiesData.map(activity => ({
        name: activity.question,
        score: activity.responses?.[0]?.score ?? 0
      })),
      totalActivityScore: activitiesData.reduce((sum, activity) => sum + (activity.responses?.[0]?.score ?? 0), 0)
    }
  } catch (error) {
    console.log(error)
    throw new Error("Failed to fetch student report.")
  }
}