import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, Text, Users } from "lucide-react";

type ModuleData = {
  id: string;
  title: string;
  description: string | null;
  course: string;
  grade: string;
  teacher: string;
  count: {
    questions: number;
    students: number;
  };
};

export function ModuleHeader({
  courseId,
  moduleData,
  sessionId,
}: {
  courseId: string;
  moduleData: ModuleData;
  sessionId: string;
}) {
  return (
    <div className="border-b pb-2">
      <Button variant="ghost" size="sm" className="mb-6 -mt-2" asChild>
        <Link href={`/dashboard/${sessionId}/courses/${courseId}`}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Modules
        </Link>
      </Button>

      <div className="space-y-2">
        <div className="flex flex-wrap gap-2">
          <Badge variant="secondary">{moduleData.grade}</Badge>
          <Badge variant="secondary">{moduleData.course}</Badge>
        </div>

        <h1 className="text-3xl font-bold text-gray-900">{moduleData.title}</h1>
        <p className="text-gray-600 text-lg mb-2">{moduleData.description}</p>
        <p className="text-gray-700">
          Teacher: <span className="font-medium">{moduleData.teacher}</span>
        </p>

        <div className="flex flex-wrap items-center gap-6 text-sm text-gray-600 mb-2">
          <div className="flex items-center gap-2">
            <Text className="h-4 w-4" />
            {moduleData.count.questions} questions
          </div>
          <div className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            {moduleData.count.students.toLocaleString()} students
          </div>
        </div>
      </div>
    </div>
  );
}
