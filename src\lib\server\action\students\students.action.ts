"use server"

import prisma from '@/lib/prisma'
import { revalidatePath } from 'next/cache'
import { TStudentForm, TUpdateStudentForm } from './students.schema'


export async function createStudent (data: TStudentForm, sessionId: string, isAdmin: boolean) {
  try {
    const existingUser = await prisma.user.findUnique({
      where: { email: data.email },
    });

    if (existingUser) {
      return { success: false, error: "User with this email already exists" };
    }

    const student = await prisma.studentProfile.create({
      data: {
        user: {
          create: {
            firstName: data.firstName,
            lastName: data.lastName,
            email: data.email,
            phone: data.phone,
            role: "STUDENT" as const,
          },
        },
        studentId: Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15),
        school: { connect: { id: data.school } },
        grade: { connect: { id: data.grade } },
        courseId: data.course,
        session: { connect: { id: sessionId } },
      },
    });

    if (isAdmin) return approveStudent(student.id)

    revalidatePath('/dashboard/students')

    return { success: true, message: 'Student created' };
  } catch (error) {
    console.error("Error creating student:", error);
    return { success: false, error: "Failed to create student" };
  }
}

export async function approveStudent (studentId: string) {
  try {
    const student = await prisma.studentProfile.findUnique({
      where: { id: studentId },
      select: {
        courseId: true,
        userId: true,
        schoolId: true,
        gradeId: true,
        sessionId: true,
      },
    });

    if (!student) {
      return { success: false, error: "Student not found" };
    }

    const loginCode = `code-${Date.now().toString(36)}${Math.random().toString(36).slice(2, 8)}`

    await prisma.$transaction([
      prisma.studentProfile.update({
        where: { id: studentId },
        data: {
          user: {
            update: {
              status: "APPROVED" as const,
            },
          },
          loginCode,
        },
      }),
      prisma.code.create({
        data: {
          user: { connect: { id: student.userId } },
          code: loginCode,
          expireTime: new Date(Date.now() + 1000 * 60 * 60 * 24 * 30),
          type: "STUDENT" as const,
          session: { connect: { id: student.sessionId } },
        },
      }),
      prisma.enrollment.create({
        data: {
          student: { connect: { id: studentId } },
          course: { connect: { id: student.courseId } },
          isActive: true,
        },
      }),
    ]);

    revalidatePath('/dashboard/students')
    revalidatePath('/dashboard/codes')

    return { success: true, message: 'Student approved' };
  } catch (error) {
    console.error("Error approving student:", error);
    return { success: false, error: "Failed to approve student" };
  }
};

export async function rejectStudent (studentId: string) {
  try {
    await prisma.studentProfile.delete({
      where: { id: studentId },
    });

    revalidatePath('/dashboard/students')

    return { success: true, message: 'Student approval rejected' };
  } catch (error) {
    console.error("Error rejecting student:", error);
    return { success: false, error: "Failed to reject student" };
  }
};

export async function updateStudent (studentId: string, enrollmentId: string, data: Partial<TUpdateStudentForm>) {
  try {
    await prisma.studentProfile.update({
      where: { id: studentId },
      data: {
        user: {
          update: {
            firstName: data.firstName,
            lastName: data.lastName,
            email: data.email,
            phone: data.phone,
            status: data.status,
            avatar: data.avatarUrl,
          },
        },
        school: { connect: { id: data.school } },
        grade: { connect: { id: data.grade } },
      },
    });

    if (data.course) {
      await prisma.enrollment.update({
        where: { id: enrollmentId },
        data: {
          course: { connect: { id: data.course } },
        },
      });
    }

    revalidatePath('/dashboard/students')

    return { success: true, message: 'Student updated' };
  } catch (error) {
    console.error("Error updating student:", error);
    return { success: false, error: "Failed to update student" };
  }
}

export async function deleteStudent (studentId: string) {
  try {
    const student = await prisma.studentProfile.delete({
      where: { id: studentId },
    });
    await prisma.user.delete({
      where: { id: student.userId },
    });

    revalidatePath('/dashboard/students')

    return { success: true };
  } catch (error) {
    console.error("Error deleting student:", error);
    return { success: false, error: "Failed to delete student" };
  }
}

