'use server'

import prisma from "@/lib/prisma"

export async function getLesson(moduleId: string) {
  try {
    const lesson = await prisma.lesson.findUnique({
      where: { moduleId },
      select: {
        id: true,
        title: true,
        description: true,
        videoUrl: true,
        duration: true,
        type: true,
        docUrl: true
      }
    })
    
    return lesson
  } catch (error) {
    console.log(error)
    return null
  }
}