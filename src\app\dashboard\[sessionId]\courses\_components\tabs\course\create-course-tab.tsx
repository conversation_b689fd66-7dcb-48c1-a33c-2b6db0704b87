import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import CourseForm from "@/app/dashboard/[sessionId]/courses/_components/forms/CourseForm";
import CourseCoverImageCard from "../../cards/course/cover-image-card";
import TeacherAssignmentCard from "../../cards/course/teacher-assignment-card";
import { CourseAssignedTeachers } from "@/lib/server/action/courses";
import { TCourseForm } from "@/lib/server/action/courses/course.schema";

export default function CreateCourseTab({
  sessionId,
  courseId,
  courseTeachers,
  courseData,
}: {
  sessionId: string;
  courseId: string;
  courseTeachers: CourseAssignedTeachers[];
  courseData: TCourseForm & { thumbnail?: string | null };
}) {
  console.log(courseData);

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Main Form */}
      <div className="lg:col-span-2 space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Course Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <CourseForm
              courseId={courseId}
              courseData={courseData}
              sessionId={sessionId}
            />
          </CardContent>
        </Card>
        {courseId && courseTeachers && (
          <TeacherAssignmentCard
            sessionId={sessionId}
            courseId={courseId}
            courseTeachers={courseTeachers}
          />
        )}
      </div>

      {/* Sidebar */}
      <div className="space-y-6 lg:col-span-1">
        {/* Cover Image */}
        <CourseCoverImageCard
          courseId={courseId}
          thumbnail={courseData.thumbnail}
        />
      </div>
    </div>
  );
}
