/*
  Warnings:

  - You are about to drop the column `templateUrl` on the `certificate_templates` table. All the data in the column will be lost.
  - You are about to drop the column `url` on the `course_resources` table. All the data in the column will be lost.
  - You are about to drop the column `docId` on the `lessons` table. All the data in the column will be lost.
  - You are about to drop the column `docUrl` on the `lessons` table. All the data in the column will be lost.
  - You are about to drop the column `videoId` on the `lessons` table. All the data in the column will be lost.
  - You are about to drop the column `videoUrl` on the `lessons` table. All the data in the column will be lost.
  - You are about to drop the column `certificateUrl` on the `student_certificates` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[fileId]` on the table `certificate_templates` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[fileId]` on the table `course_resources` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[fileId]` on the table `lessons` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `fileId` to the `certificate_templates` table without a default value. This is not possible if the table is not empty.
  - Added the required column `fileUrl` to the `certificate_templates` table without a default value. This is not possible if the table is not empty.
  - Added the required column `fileId` to the `lessons` table without a default value. This is not possible if the table is not empty.
  - Added the required column `fileUrl` to the `lessons` table without a default value. This is not possible if the table is not empty.

*/
-- DropIndex
DROP INDEX `lessons_docId_key` ON `lessons`;

-- DropIndex
DROP INDEX `lessons_videoId_key` ON `lessons`;

-- AlterTable
ALTER TABLE `certificate_templates` DROP COLUMN `templateUrl`,
    ADD COLUMN `fileId` VARCHAR(191) NOT NULL,
    ADD COLUMN `fileSize` INTEGER NULL,
    ADD COLUMN `fileUrl` VARCHAR(191) NOT NULL,
    ADD COLUMN `mimeType` VARCHAR(191) NULL,
    ADD COLUMN `originalName` VARCHAR(191) NULL;

-- AlterTable
ALTER TABLE `course_resources` DROP COLUMN `url`,
    ADD COLUMN `fileId` VARCHAR(191) NULL,
    ADD COLUMN `fileUrl` VARCHAR(191) NULL,
    ADD COLUMN `originalName` VARCHAR(191) NULL;

-- AlterTable
ALTER TABLE `lessons` DROP COLUMN `docId`,
    DROP COLUMN `docUrl`,
    DROP COLUMN `videoId`,
    DROP COLUMN `videoUrl`,
    ADD COLUMN `fileId` VARCHAR(191) NOT NULL,
    ADD COLUMN `fileSize` INTEGER NULL,
    ADD COLUMN `fileUrl` VARCHAR(191) NOT NULL,
    ADD COLUMN `mimeType` VARCHAR(191) NULL,
    ADD COLUMN `originalName` VARCHAR(191) NULL;

-- AlterTable
ALTER TABLE `student_certificates` DROP COLUMN `certificateUrl`,
    ADD COLUMN `fileId` VARCHAR(191) NULL,
    ADD COLUMN `fileSize` INTEGER NULL,
    ADD COLUMN `fileUrl` VARCHAR(191) NULL,
    ADD COLUMN `mimeType` VARCHAR(191) NULL,
    ADD COLUMN `originalName` VARCHAR(191) NULL;

-- CreateIndex
CREATE UNIQUE INDEX `certificate_templates_fileId_key` ON `certificate_templates`(`fileId`);

-- CreateIndex
CREATE UNIQUE INDEX `course_resources_fileId_key` ON `course_resources`(`fileId`);

-- CreateIndex
CREATE UNIQUE INDEX `lessons_fileId_key` ON `lessons`(`fileId`);
