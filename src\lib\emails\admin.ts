import { transporter } from "../mailTransporter";

export async function adminWelcomeMail(email: string, firstName: string, password: string) {
  await transporter.sendMail({
    from: process.env.SMTP_USER,
    to: email,
    subject: 'Welcome to CEAP-NCR Virtue Series',
    text: `Dear ${firstName},\n\nWelcome to CEAP-NCR Virtue Series! We are pleased to inform you that you've been added to our admin team.\n\nThis is your login credentials:\n\nEmail: ${email}\n\nPassword: ${password}\n\nIf you have any questions, feel free to reach out.\n\nBest regards,\nThe CEAP-NCR Virtue Series Team`,
  });
}
