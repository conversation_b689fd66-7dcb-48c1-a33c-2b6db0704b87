"use client";

import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { Form } from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { Loader, Save } from "lucide-react";
import { FormInputField } from "@/components/form-element/input-field";
import { FormTextareaField } from "@/components/form-element/text-area";
import {
  assessmentSchema,
  TAssessmentForm,
} from "@/lib/server/action/courses/modules/assessments/assessments.schema";
import {
  createAssessment,
  updateAssessment,
} from "@/lib/server/action/courses/modules/assessments";

export default function AssessmentForm({
  assessmentData,
  moduleId,
  assessmentId,
}: {
  assessmentData?: TAssessmentForm;
  moduleId?: string;
  assessmentId?: string;
}) {
  const form = useForm<TAssessmentForm>({
    resolver: zodResolver(assessmentSchema),
    defaultValues: assessmentData ?? {
      passingScore: 70.0,
      maxStudentQuestions: 20,
      instructions: "",
    },
    mode: "onChange",
  });

  const onSubmit = async (values: TAssessmentForm) => {
    const res = assessmentData
      ? await updateAssessment(assessmentId as string, values)
      : await createAssessment(moduleId as string, values);
    if (res.success) {
      toast.success(res.message);
    } else {
      toast.error(res.error);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="w-full">
        <div className="flex flex-col gap-y-5">
          <FormInputField
            type="number"
            control={form.control}
            name="passingScore"
            label="Passing Score"
          />
          <FormInputField
            type="number"
            control={form.control}
            name="maxStudentQuestions"
            label="Max Student Questions"
          />
          <FormTextareaField
            control={form.control}
            name="instructions"
            label="Instructions (Optional)"
            placeholder="Enter instructions"
          />
        </div>
        <div className="flex w-full pt-4">
          <Button
            type="submit"
            disabled={form.formState.isSubmitting}
            className="w-full"
          >
            {form.formState.isSubmitting ? (
              <>
                <Loader /> Saving Assessment
              </>
            ) : (
              <>
                <Save /> Save Assessment
              </>
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}
