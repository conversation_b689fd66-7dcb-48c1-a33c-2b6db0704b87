"use server"

import prisma from '@/lib/prisma'
import { PublishStatus } from '@prisma/client'
import { revalidatePath } from 'next/cache'
import { TCourseForm, TUpdateCourseForm } from './course.schema';
import { generateSlug } from '@/lib/generateSlug';


export async function createCourse(sessionId: string, data: TCourseForm & { thumbnail?: string, status?: PublishStatus }) {
  try {
    const slug = generateSlug(data.title)

    const existingCourse = await prisma.course.findUnique({
      where: { slug },
    });

    if (existingCourse) {
      return { success: false, error: "Course with this code already exists" };
    }

    await prisma.course.create({
      data: {
        title: data.title,
        slug,
        description: data.description,
        order: await getNextCourseOrder(data.gradeId),
        grade: { connect: { id: data.gradeId } },
        thumbnail: data.thumbnail,
        status: data.status,
        session: { connect: { id: sessionId } },
        teacherAssignments: {
          create: {
            teacher: { connect: { id: data.teacherId } },
            role: 'PRIMARY'
          }
        }
      },
    })
    
    revalidatePath('/admin/courses')
    return { success: true, message: 'Course created' }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to create course' }
  }
}

export async function updateCourse(courseId: string, data: Partial<TUpdateCourseForm>) {
  try {
    await prisma.course.update({
      where: { id: courseId },
      data: {
        title: data.title,
        description: data.description,
        isActive: data.isActive,
        status: data.status,
        thumbnail: data.thumbnail,
        grade: { connect: { id: data.gradeId } },
      },
    })
    
    revalidatePath('/admin/courses')
    revalidatePath(`/admin/courses/${courseId}`)
    return { success: true, message: 'Course updated' }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to update course' }
  }
}

export async function deleteCourse(courseId: string) {
  try {
    await prisma.course.delete({
      where: { id: courseId }
    })
    
    revalidatePath('/admin/courses')
    return { success: true }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to delete course' }
  }
}

export async function reorderCourse(courseIds: string[]) {
  try {
    const updatePromises = courseIds.map((course: string, index: number) => {
      console.log(course, index)
      prisma.course.update({
        where: { id: course },
        data: { order: index }
      })
    })
    
    await Promise.all(updatePromises)

    revalidatePath('/admin/courses')
    
    return { success: true }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to change course order' }
  }
}

// Teacher Assignment Actions
export async function assignCoursePrimaryTeacher(courseId: string, teacherId: string) {
  try {
    const existingPrimaryTeacher = await prisma.teacherCourseAssignment.findFirst({
      where: {
        courseId: courseId,
        role: 'PRIMARY'
      }
    })
    
    if (existingPrimaryTeacher) {
      await prisma.teacherCourseAssignment.delete({
        where: { id: existingPrimaryTeacher.id }
      })
    }

    await prisma.teacherCourseAssignment.create({
      data: {
        teacherId,
        courseId,
        role: 'PRIMARY'
      }
    })
    
    revalidatePath(`/admin/courses/${courseId}`)
    return { success: true, message: 'Primary teacher assigned' }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to assign primary teacher' }
  }
}

export async function assignCourseAssistantTeacher(courseId: string, teacherId: string) {
  try {
    await prisma.teacherCourseAssignment.create({
      data: {
        teacherId,
        courseId,
        role: 'ASSISTANT'
      }
    })
    
    revalidatePath(`/admin/courses/${courseId}`)
    return { success: true, message: 'Teacher assigned' }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to assign assistant teacher' }
  }
}

export async function assignCertificateTemplate(courseId: string, templateId: string) {
  try {
    const existingTemplate = await prisma.assignCertificateToCourse.findFirst({
      where: { courseId }
    })
    
    if (existingTemplate) {
      await prisma.assignCertificateToCourse.delete({
        where: { id: existingTemplate.id }
      })
    }

    await prisma.assignCertificateToCourse.create({
      data: {
        courseId,
        templateId
      }
    })
    
    revalidatePath(`/admin/courses/${courseId}`)
    return { success: true, message: 'Certificate template assigned' }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to assign certificate template' }
  }
}

export async function removeCourseTeacher(teacherAssignmentId: string) {
  try {
    await prisma.teacherCourseAssignment.delete({
      where: { id: teacherAssignmentId }
    })

    revalidatePath(`/admin/courses`)

    return { success: true, message: 'Teacher removed' }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to remove teacher' }
  }
}

// Utility functions
async function getNextCourseOrder(gradeId: string): Promise<number> {
  const lastCourse = await prisma.course.findFirst({
    where: { gradeId },
    orderBy: { order: 'desc' }
  })
  return (lastCourse?.order || 0) + 1
}