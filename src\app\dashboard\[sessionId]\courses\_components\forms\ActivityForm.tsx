"use client";

import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { Form } from "@/components/ui/form";
import { Loader } from "lucide-react";
import { toast } from "sonner";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  createActivity,
  updateActivity,
} from "@/lib/server/action/courses/activities";
import {
  activitySchema,
  TActivityForm,
} from "@/lib/server/action/courses/activities/activities.schema";
import { FormTextareaField } from "@/components/form-element/text-area";
import { FormDatePickerField } from "@/components/form-element/date-picker";

export default function ActivityForm({
  activityId,
  activityData,
  courseId,
}: {
  courseId?: string;
  activityId?: string;
  activityData?: TActivityForm;
}) {
  const form = useForm<TActivityForm>({
    resolver: zodResolver(activitySchema),
    defaultValues: activityData ?? {
      question: "",
      deadline: new Date(Date.now() + 1000 * 60 * 60 * 24 * 7),
    },
    mode: "onChange",
  });

  const onSubmit = async (values: TActivityForm) => {
    const res = activityData
      ? await updateActivity(activityId as string, values)
      : await createActivity(courseId as string, values);
    if (res.success) {
      toast.success(res.message);
    } else {
      toast.error(res.error);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormTextareaField
          control={form.control}
          name="question"
          label="Question"
          placeholder="Enter activity question"
        />
        <FormDatePickerField
          control={form.control}
          name="deadline"
          label="Deadline"
          placeholder="Select a deadline"
        />
        <div className="flex justify-end gap-2">
          <Button type="submit" disabled={form.formState.isSubmitting}>
            {form.formState.isSubmitting ? (
              <>
                <Loader />{" "}
                {activityData ? "Updating Activity" : "Create Activity"}
              </>
            ) : (
              <>{activityData ? "Update Activity" : "Create Activity"}</>
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}
