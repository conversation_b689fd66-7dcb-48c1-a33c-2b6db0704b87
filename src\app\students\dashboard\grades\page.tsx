import { auth } from "../../../../../auth";
import { GradeCard } from "../_components/cards/GradeCard";

const gradeData = [
  {
    title: "Grade 1",
    description:
      "Comprehensive examination covering fundamental programming concepts, data structures, and algorithmic thinking. This assessment evaluates your understanding of core CS principles.",
  },
];

export default async function GradesPage() {
  const session = await auth();
  if (!session || !session.user) {
    return null;
  }

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">My Grade</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {gradeData.map((grade, index) => (
          <GradeCard
            key={index}
            {...grade}
            studentId={session.user.profileId}
          />
        ))}
      </div>
    </div>
  );
}
