"use client";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { PublishStatus } from "@prisma/client";
import { Filter, Search } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";

export default function CourseFilter({
  grades,
}: {
  grades: { label: string; value: string }[];
}) {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedGrade, setSelectedGrade] = useState<string>("all");
  const [selectedStatus, setSelectedStatus] = useState<PublishStatus | "all">(
    "PUBLISHED"
  );

  const router = useRouter();

  const handleSearch = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    const params = new URLSearchParams(window.location.search);
    params.set("search", searchTerm);
    router.push(`${window.location.pathname}?${params}`);
  };

  const handleGradeChange = (value: string) => {
    setSelectedGrade(value);

    const params = new URLSearchParams(window.location.search);
    params.set("grade", value);
    router.push(`${window.location.pathname}?${params}`);
  };

  const handleStatusChange = (value: PublishStatus) => {
    setSelectedStatus(value);

    const params = new URLSearchParams(window.location.search);
    params.set("status", value);
    router.push(`${window.location.pathname}?${params}`);
  };

  return (
    <div className="flex flex-col lg:flex-row gap-6">
      <form onSubmit={handleSearch} className="relative flex-1">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
        <Input
          placeholder="Search courses, instructors, or categories..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10"
        />
      </form>
      <div className="flex flex-wrap gap-3 items-center">
        <div className="flex items-center gap-2">
          <Filter className="h-4 w-4 text-gray-500" />
          <span className="text-sm font-medium text-gray-700">Filter by:</span>
        </div>

        <Select value={selectedGrade} onValueChange={handleGradeChange}>
          <SelectTrigger className="w-32">
            <SelectValue placeholder="Filter by Grade" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Grades</SelectItem>
            {grades.map((grade) => (
              <SelectItem key={grade.value} value={grade.value}>
                {grade.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={selectedStatus} onValueChange={handleStatusChange}>
          <SelectTrigger className="w-32">
            <SelectValue placeholder="Filter by Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="PUBLISHED">Published</SelectItem>
            <SelectItem value="DRAFT">Draft</SelectItem>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="ARCHIVED">Archived</SelectItem>
          </SelectContent>
        </Select>

        <Button
          variant="outline"
          size="sm"
          onClick={() => {
            setSearchTerm("");
            setSelectedGrade("all");
            setSelectedStatus("PUBLISHED");
            router.push(`${window.location.pathname}?`);
          }}
        >
          Clear Filters
        </Button>
      </div>
    </div>
  );
}
