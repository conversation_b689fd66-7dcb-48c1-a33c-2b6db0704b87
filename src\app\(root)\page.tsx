import AboutSection from "./_components/sections/about-section";
import HeroSection from "./_components/sections/hero-section";
import NewsletterSection from "./_components/sections/newsletter-section";
import PartnersSection from "./_components/sections/partners-section";
import SubscriptionSection from "./_components/sections/subscription-section";

const mockData = {
  hero: {
    title: "UNLOCK YOUR POTENTIAL WITH ONLINE LEARNING",
    subtitle:
      "Upgrade your skills by learning online with the convenience of home.",
    image: "/images/placeholder.svg",
    supportText: "Providing support to over 1k schools in the MaPSA - Antipolo",
  },
  partners: {
    title: "Trusted by Leading Educational Institutions",
    logos: [
      { name: "Partner 1", icon: "🏛️" },
      { name: "Partner 2", icon: "🎯" },
      { name: "Partner 3", icon: "⚡" },
      { name: "Partner 4", icon: "🌟" },
      { name: "Partner 5", icon: "🔬" },
      { name: "Partner 6", icon: "📚" },
    ],
  },
  subscription: {
    title:
      "We Invite You to Subscribe to the MaPSA Antipolo Educational System!",
    description:
      "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries.",
    ctaButton: {
      text: "Learn more",
      href: "/learn-more",
    },
    image: "/images/placeholder.svg?height=400&width=300",
  },
  about: {
    title:
      "How the MaPSA - Antipolo Educational System Nurtures the Students and Educators!",
    description:
      "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries.",
    ctaButton: {
      text: "Learn more",
      href: "/about",
    },
    image: "/images/placeholder.svg?height=400&width=300",
  },
  newsletter: {
    title: "Subscribe your email for latest updates!",
    placeholder: "Enter your email",
    buttonText: "Subscribe",
  },
};

export default function HomePage() {
  return (
    <>
      <HeroSection data={mockData.hero} />
      <PartnersSection data={mockData.partners} />
      <SubscriptionSection data={mockData.subscription} />
      <AboutSection data={mockData.about} />
      <NewsletterSection data={mockData.newsletter} />
    </>
  );
}
