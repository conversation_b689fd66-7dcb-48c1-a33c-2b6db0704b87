'use server'

import prisma from "@/lib/prisma"
import { revalidatePath } from "next/cache"


export async function getAssessmentForAttempt(attemptId: string, takenQuestions: number) {
  const attempt = await prisma.assessmentAttempt.findUnique({
    where: { id: attemptId },
    select: {
      id: true,
      assessment: {
        select: {
          id: true,
          questions: {
            select: {
              id: true,
              question: true,
              questionType: true,
              options: true,
              correctAnswer: true,
              order: true,
              points: true
            },
            orderBy: { order: 'asc' },
            take: takenQuestions
          }
        }
      },
      responses: true
    }
  })

  if (!attempt) return null

  const assessment = {
    assessmentId: attempt.assessment.id,
    questions: attempt.assessment.questions.map(question => ({
      id: question.id,
      question: question.question,
      questionType: question.questionType as "multiple-choice" | "true-false",
      options: JSON.parse(question.options as string),
      correctAnswer: question.correctAnswer,
      order: question.order,
      points: question.points
    }))
  }
    
  return { assessment, responses: attempt.responses }
}

export async function getStudentResults(studentId: string) {
  return await prisma.assessmentAttempt.findMany({
    where: { 
      studentId,
      status: 'COMPLETED'
    },
    include: {
      assessment: {
        include: {
          module: {
            include: {
              course: true
            }
          }
        }
      }
    },
    orderBy: { completedAt: 'desc' }
  })
}

export async function enrollInCourse(studentId: string, courseId: string) {
  try {
    const enrollment = await prisma.enrollment.create({
      data: {
        studentId,
        courseId
      }
    })
    
    revalidatePath('/student/dashboard')
    return { success: true, enrollment }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to enroll in course' }
  }
}

export async function getCourseDetails(courseId: string, studentId: string) {
  const course = await prisma.course.findUnique({
    where: { id: courseId },
    include: {
      grade: true,
      modules: {
        include: {
          resources: true,
          assessment: {
            include: {
              attempts: {
                where: { studentId },
                orderBy: { attemptNumber: 'desc' }
              }
            }
          },
          progress: {
            where: { studentId }
          }
        },
        orderBy: { order: 'asc' }
      },
      enrollments: {
        where: { studentId }
      }
    }
  })
  
  return course
}