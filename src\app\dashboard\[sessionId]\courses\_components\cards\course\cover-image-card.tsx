"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { uploadFileToBunny } from "@/lib/bunny";
import { generateFileUploadUrl } from "@/lib/server/action/bunny/bunny.action";
import { updateCourse } from "@/lib/server/action/courses";
import { ImageIcon, Upload } from "lucide-react";
import Image from "next/image";
import { useState } from "react";
import { toast } from "sonner";

export default function CourseCoverImageCard({
  courseId,
  fileId,
  fileUrl,
}: {
  courseId?: string;
  fileId?: string;
  fileUrl?: string;
}) {
  const [file, setFile] = useState<File | null>(null);
  const [coverImage, setCoverImage] = useState(fileUrl ?? "");
  const [uploadState, setUploadState] = useState({
    progress: 0,
    isUploading: false,
    uploadedBytes: 0,
    totalSize: 0,
  });

  const updateUploadProgress = (
    percentage: number,
    loaded: number,
    total: number
  ) => {
    setUploadState({
      progress: Math.round(percentage),
      isUploading: true,
      uploadedBytes: loaded,
      totalSize: total,
    });
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setFile(file);
      setCoverImage(URL.createObjectURL(file));
    }
  };

  const handleImageUpload = async (
    event: React.FormEvent<HTMLFormElement>
  ) => {
    event.preventDefault();
    if (!file) {
      toast.error("Please select a file");
      return;
    }
    
    setUploadState((prev) => ({
      ...prev,
      isUploading: true,
      progress: 0,
      uploadedBytes: 0,
      totalSize: 0,
    }));

    try {
      const { fileId, uploadUrl, accessKey, cdnUrl } =
        await generateFileUploadUrl(file.name);
      
      await uploadFileToBunny(file, uploadUrl, accessKey, updateUploadProgress);

      //Upload image
      setCoverImage(file.name);
      const res = await updateCourse(courseId as string, {
        fileId,
        fileUrl: cdnUrl,
      });
      if (res.success) {
        toast.success(res.message);
      } else {
        toast.error(res.error);
      }
    } catch (error) {
      console.error(error);
      toast.error("An error occurred while uploading the file");
    } finally {
      setUploadState((prev) => ({
        ...prev,
        isUploading: false,
      }));
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Course Cover Image</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleImageUpload} className="space-y-4">
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
            {coverImage ? (
              <div className="space-y-2">
                <Image
                  src={coverImage}
                  alt="Course Cover"
                  width={100}
                  height={100}
                />
              </div>
            ) : (
              <div className="space-y-2">
                <Upload className="w-8 h-8 mx-auto text-gray-400" />
                <p className="text-sm text-gray-600">
                  Upload course cover image
                </p>
              </div>
            )}
          </div>
          {uploadState.isUploading && (
            <div className="flex items-center justify-between">
              <p className="text-sm text-gray-600">
                Uploading {uploadState.uploadedBytes} of {uploadState.totalSize}{" "}
                bytes ({uploadState.progress}%)...
              </p>
            </div>
          )}
          <Input
            type="file"
            accept="image/*"
            onChange={(e) => handleChange(e)}
            className="cursor-pointer"
          />
          <button type="submit" className="btn btn-primary">
            Upload
          </button>
        </form>
      </CardContent>
    </Card>
  );
}
