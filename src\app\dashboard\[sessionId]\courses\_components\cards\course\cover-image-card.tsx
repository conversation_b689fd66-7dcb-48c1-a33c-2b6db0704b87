"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { updateCourse } from "@/lib/server/action/courses";
import { ImageIcon, Upload } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

export default function CourseCoverImageCard({
  courseId,
  thumbnail,
}: {
  courseId?: string;
  thumbnail?: string | null;
}) {
  const [coverImage, setCoverImage] = useState(thumbnail ?? "");

  const handleImageUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (file) {
      //Upload image
      setCoverImage(file.name);
      const res = await updateCourse(courseId as string, {
        thumbnail: file.name,
      });
      if (res.success) {
        toast.success(res.message);
      } else {
        toast.error(res.error);
      }
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Course Cover Image</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
            {coverImage ? (
              <div className="space-y-2">
                <ImageIcon className="w-8 h-8 mx-auto text-green-600" />
                <p className="text-sm text-green-600">{coverImage}</p>
              </div>
            ) : (
              <div className="space-y-2">
                <Upload className="w-8 h-8 mx-auto text-gray-400" />
                <p className="text-sm text-gray-600">
                  Upload course cover image
                </p>
              </div>
            )}
          </div>
          <Input
            type="file"
            accept="image/*"
            onChange={handleImageUpload}
            className="cursor-pointer"
          />
        </div>
      </CardContent>
    </Card>
  );
}
