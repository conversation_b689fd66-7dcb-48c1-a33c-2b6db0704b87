import { Card, CardContent } from "@/components/ui/card";
import { getTimeLeft } from "@/lib/timeLeft";
import { cn } from "@/lib/utils";

type RecentFilesProps = {
  id: string;
  name: string;
  type: string;
  fileSize: string;
  viewedAt: Date;
};

export default function RecentFilesSection({
  recentFiles,
}: {
  recentFiles: RecentFilesProps[];
}) {
  const fileTypeColor = (type: string) => {
    switch (type.toLowerCase()) {
      case "pdf":
        return "bg-red-400 text-red-800";
      case "zip":
        return "bg-yellow-400 text-yellow-800";
      case "docx":
        return "bg-blue-400 text-blue-800";
      case "xlsx":
        return "bg-green-400 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div>
      <h3 className="text-lg font-semibold mb-4">Recent files</h3>
      {recentFiles.length === 0 ? (
        <p className="text-sm text-muted-foreground">No recent files found.</p>
      ) : (
        <div className="space-y-2">
          {recentFiles.map((file) => (
            <Card
              key={file.id}
              className="hover:shadow-md transition-shadow cursor-pointer py-0"
            >
              <CardContent className="p-4">
                <div className="flex items-center gap-4">
                  <div className="p-1 bg-blue-100 rounded-lg flex items-center justify-center">
                    <div
                      className={cn(
                        "h-10 w-11 bg-primary rounded-sm flex items-center justify-center",
                        fileTypeColor(file.type)
                      )}
                    >
                      <span className="text-white text-xs font-bold">
                        {file.type.slice(0, 4).toUpperCase()}
                      </span>
                    </div>
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium">{file.name}</h4>
                    <p className="text-sm text-gray-500">{file.type}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-gray-500">{file.fileSize}</p>
                    <p className="text-xs text-gray-400">
                      {getTimeLeft(file.viewedAt)}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
