"use client";

import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Eye } from "lucide-react";
import Link from "next/link";

interface GradeCardProps {
  studentId: string;
  title: string;
  description: string;
}

export function GradeCard({ studentId, title, description }: GradeCardProps) {
  return (
    <Card className="hover:shadow-lg transition-shadow duration-200 gap-0">
      <CardHeader className="pb-3">
        <h3 className="font-semibold text-lg leading-tight">{title}</h3>
      </CardHeader>

      <CardContent className="space-y-4">
        <p className="text-sm text-muted-foreground line-clamp-2">
          {description}
        </p>

        {/* Action Button */}
        <Button className="w-full" asChild>
          <Link href={`/students/${studentId}`}>
            <Eye className="w-4 h-4" />
            View Courses
          </Link>
        </Button>
      </CardContent>
    </Card>
  );
}
