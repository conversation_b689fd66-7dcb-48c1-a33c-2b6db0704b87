import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import {
  BarChart3,
  Users,
  FolderOpen,
  Video,
  BookOpen,
  ArrowLeft,
  Text,
} from "lucide-react";
import { CourseAnalyticsTab } from "../_components/tabs/course/analytics-tab";
import { VirtualClassroomTab } from "../_components/tabs/course/virtual-classroom-tab";
import { StudentsTab } from "../_components/tabs/course/students-tab";
import { ModulesTab } from "../_components/tabs/course/modules-tab";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import Image from "next/image";
import { checkCourse } from "@/lib/server/action/courses";
import { notFound } from "next/navigation";
import DeleteCourse from "../_components/DeleteCourse";
import ActivitiesTab from "../_components/tabs/course/activities-tab";
import FileManagementTab from "../_components/tabs/course/file-management-tab";

interface CourseDetailPageProps {
  params: Promise<{ sessionId: string; courseId: string }>;
  searchParams: Promise<{ [key: string]: string | undefined }>;
}

export default async function CourseDetailPage({
  params,
  searchParams,
}: CourseDetailPageProps) {
  const { sessionId, courseId } = await params;
  const checkValidCourse = await checkCourse(courseId);
  if (!checkValidCourse) {
    notFound();
  }

  return (
    <>
      {/* back button */}
      <div className="flex items-center justify-between gap-2 mb-3 -mt-2">
        <Button variant="ghost" size="sm" asChild>
          <Link href={`/dashboard/${sessionId}/courses`}>
            <ArrowLeft className="h-4 w-4" />
            Back to Courses
          </Link>
        </Button>
        <DeleteCourse courseId={courseId} sessionId={sessionId} />
      </div>

      {/* Header with Cover Photo */}
      <div className="relative h-48  overflow-hidden rounded-lg">
        <Image
          src={"/images/placeholder.svg"}
          alt="course cover"
          fill
          className="object-cover"
        />
      </div>

      {/* Navigation Tabs */}
      <Tabs defaultValue="analytics" className="w-full mt-2">
        <TabsList className="w-full h-full flex-wrap py-2 gap-1 shrink-0">
          <TabsTrigger
            value="analytics"
            className="flex items-center px-4 gap-2 data-[state=active]:rounded-lg data-[state=active]:border-primary data-[state=active]:text-primary h-full cursor-pointer"
          >
            <BarChart3 className="w-4 h-4" />
            Analytics
          </TabsTrigger>
          <TabsTrigger
            value="students"
            className="flex items-center px-4 gap-2 data-[state=active]:rounded-lg data-[state=active]:border-primary data-[state=active]:text-primary h-full cursor-pointer"
          >
            <Users className="w-4 h-4" />
            Students
          </TabsTrigger>
          <TabsTrigger
            value="modules"
            className="flex items-center px-4 gap-2 data-[state=active]:rounded-lg data-[state=active]:border-primary data-[state=active]:text-primary h-full cursor-pointer"
          >
            <BookOpen className="w-4 h-4" />
            Modules
          </TabsTrigger>
          <TabsTrigger
            value="activities"
            className="flex items-center px-4 gap-2 data-[state=active]:rounded-lg data-[state=active]:border-primary data-[state=active]:text-primary h-full cursor-pointer"
          >
            <Text className="w-4 h-4" />
            Activities
          </TabsTrigger>
          <TabsTrigger
            value="files"
            className="flex items-center px-4 gap-2 data-[state=active]:rounded-lg data-[state=active]:border-primary data-[state=active]:text-primary h-full cursor-pointer"
          >
            <FolderOpen className="w-4 h-4" />
            File Management
          </TabsTrigger>
          <TabsTrigger
            value="classroom"
            className="flex items-center px-4 gap-2 data-[state=active]:rounded-lg data-[state=active]:border-primary data-[state=active]:text-primary h-full cursor-pointer"
          >
            <Video className="w-4 h-4" />
            Virtual Classroom
          </TabsTrigger>
        </TabsList>

        <div className="py-6">
          <TabsContent value="analytics" className="mt-0">
            <CourseAnalyticsTab courseId={courseId} sessionId={sessionId} />
          </TabsContent>

          <TabsContent value="students" className="mt-0">
            <StudentsTab courseId={courseId} sessionId={sessionId} />
          </TabsContent>

          <TabsContent value="modules" className="mt-0">
            <ModulesTab
              courseId={courseId}
              sessionId={sessionId}
              searchParams={searchParams}
            />
          </TabsContent>

          <TabsContent value="activities" className="mt-0">
            <ActivitiesTab courseId={courseId} sessionId={sessionId} />
          </TabsContent>

          <TabsContent value="files" className="mt-0">
            <FileManagementTab courseId={courseId} />
          </TabsContent>

          <TabsContent value="classroom" className="mt-0">
            <VirtualClassroomTab courseId={courseId} />
          </TabsContent>
        </div>
      </Tabs>
    </>
  );
}
