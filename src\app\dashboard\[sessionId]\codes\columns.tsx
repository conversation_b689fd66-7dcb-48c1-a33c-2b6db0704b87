"use client";

import CustomDialog from "@/components/shared/CustomDialog";
import { But<PERSON> } from "@/components/ui/button";
import { ColumnDef } from "@tanstack/react-table";
import { Eye, RotateCcw } from "lucide-react";
import ViewCode from "./_components/ViewCode";
import {
  reactivateCode,
  CodeWithUserAndSchool,
} from "@/lib/server/action/codes";
import { getTimeLeft } from "@/lib/timeLeft";
import CustomAlertDialog from "@/components/shared/CustomAlertDialog";
import { toast } from "sonner";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

export const columns: ColumnDef<CodeWithUserAndSchool>[] = [
  {
    accessorKey: "user",
    header: () => <div className="ml-2">Name</div>,
    cell: ({ row }) => {
      const user = row.original.user;
      return (
        <div className="ml-2 font-medium truncate">{`${user.firstName} ${user.lastName}`}</div>
      );
    },
  },
  {
    accessorKey: "email",
    header: "Email",
    cell: ({ row }) => {
      const user = row.original.user;
      return <div className="ml-2 font-medium truncate">{user.email}</div>;
    },
  },
  {
    accessorKey: "code",
    header: "Code",
    cell: ({ row }) => {
      return (
        <div
          className={`font-medium ${
            row.original.status == "ACTIVE" ? "text-green-500" : "text-red-500"
          }`}
        >
          {row.getValue("code")}
        </div>
      );
    },
  },
  {
    accessorKey: "type",
    header: "Type",
    cell: ({ row }) => {
      return (
        <Badge
          className={cn(
            row.getValue("type") == "TEACHER" ? "bg-blue-100" : "bg-green-100",
            "text-black"
          )}
        >
          {row.getValue("type") == "TEACHER" ? "Teacher" : "Student"}
        </Badge>
      );
    },
  },
  {
    accessorKey: "expireTime",
    header: "Exam Exp.",
    cell: ({ row }) => {
      return (
        <div className="ml-2 font-medium truncate">
          {row.original.type === "TEACHER"
            ? "N/A"
            : getTimeLeft(row.getValue("expireTime"))}
        </div>
      );
    },
  },
  {
    accessorKey: "action",
    header: "Action",
    cell: ({ row }) => {
      const code = row.original;
      const isExpired =
        (code.expireTime || 0) <= new Date() && code.type === "STUDENT";

      return (
        <div className="flex items-center gap-2">
          <CustomDialog
            trigger={
              <Button size="icon">
                <Eye />
              </Button>
            }
            title="Code Details"
          >
            <ViewCode
              name={`${code.user.firstName} ${code.user.lastName}`}
              email={code.user.email}
              code={code.code}
              status={code.status}
              type={code.type}
            />
          </CustomDialog>
          {isExpired ? (
            <CustomAlertDialog
              trigger={
                <Button size="icon" variant="outline">
                  <RotateCcw />
                </Button>
              }
              title="Reactivate Code"
              description="This action will reactivate this user's code. Do you wish to continue?"
              normal
              onConfirm={async () => {
                const res = await reactivateCode(code.id);
                if (res.success) {
                  toast.success("Code reactivated");
                } else {
                  toast.error("Something went wrong, Please try again later.");
                }
              }}
            />
          ) : null}
        </div>
      );
    },
  },
];
