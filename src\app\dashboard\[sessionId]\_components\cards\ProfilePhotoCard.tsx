"use client";

import { Avatar, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { validateImageFile } from "@/lib/file-validators";
// import { updateAdminPhoto } from "@/lib/server/user/user.action";
// import {
//   deleteFileByType,
//   uploadImage,
// } from "@/lib/server/fileUpload/fileUpload.action";
import { Loader } from "lucide-react";
import { ChangeEvent, useRef, useState } from "react";
import { toast } from "sonner";

type TUser = {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  avatar: string;
  role: string;
  createdAt: Date;
};

const ProfilePhotoCard = ({ user }: { user: TUser }) => {
  const [uploading, setUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const maxSizeMB = 5;
  console.log(user);

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const selectedFile = e.target.files[0];
      processFile(selectedFile);
    }
  };

  const processFile = (selectedFile: File) => {
    // Validate file
    const validation = validateImageFile(selectedFile, maxSizeMB);
    if (!validation.valid) {
      toast.error(validation.error || "Invalid file");
      return;
    }

    handleUpload(selectedFile);
  };

  const handleUpload = async (fileToUpload: File) => {
    if (!fileToUpload) return;

    setUploading(true);

    try {
      const formData = new FormData();
      formData.append("file", fileToUpload);

      // const result = (await uploadImage(formData)) as {
      //   success: boolean;
      //   url: string;
      //   fileId: string;
      //   error?: undefined;
      // };

      // if (result.success) {
      //   try {
      //     const res = await updateAdminPhoto(user.id, {
      //       userPhotoId: result.fileId,
      //       userPhotoUrl: result.url,
      //     });
      //     toast(res.message);
      //   } catch (error) {
      //     console.error(error);
      //     await deleteFileByType(result.fileId, "image");
      //     toast.error("An error occurred while uploading the file");
      //   }
      // } else {
      //   toast.error(result.error || "Upload failed");
      // }
    } catch (err) {
      console.error(err);
      toast.error("Upload failed");
    } finally {
      setUploading(false);
    }
  };

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle>Profile</CardTitle>
          <CardDescription>Manage your profile picture</CardDescription>
        </CardHeader>
        <CardContent className="flex flex-col items-center space-y-4">
          <Avatar className="h-24 w-24">
            <AvatarImage src={"/placeholder-user.jpg"} alt="User" />
            {/* <AvatarFallback>{`${user.firstName[0].toUpperCase()}${user.lastName[0].toUpperCase()}`}</AvatarFallback> */}
          </Avatar>
          <div className="flex gap-2">
            <Button
              size="sm"
              variant="outline"
              onClick={async () => {
                // const res = await updateAdminPhoto(user.id, {
                //   userPhotoId: "",
                //   userPhotoUrl: "",
                // });
                // if (res.success) {
                //   toast("Removed successfully");
                // } else {
                //   toast(res.message);
                // }
              }}
            >
              Remove
            </Button>
            <Button
              size="sm"
              disabled={uploading}
              onClick={() => fileInputRef.current?.click()}
              className="disabled:opacity/20"
            >
              {uploading ? (
                <>
                  <Loader /> Uploading
                </>
              ) : (
                "Upload"
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      <input
        ref={fileInputRef}
        id="userPhoto"
        name="userPhoto"
        type="file"
        accept="image/*"
        className="hidden"
        onChange={handleFileChange}
      />
    </>
  );
};

export default ProfilePhotoCard;
