import { Button } from "@/components/ui/button";
import Image from "next/image";
import Link from "next/link";

interface AboutSectionProps {
  data: {
    title: string;
    description: string;
    ctaButton: {
      text: string;
      href: string;
    };
    image: string;
  };
}

export default function AboutSection({ data }: AboutSectionProps) {
  return (
    <section className="w-full py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="grid items-center gap-8 lg:grid-cols-2 lg:gap-12">
          <div className="flex flex-col justify-center space-y-6">
            <h2 className="text-3xl font-bold text-gray-900 lg:text-4xl">
              {data.title}
            </h2>
            <p className="text-gray-600 leading-relaxed">{data.description}</p>
            <Button asChild className="w-fit bg-blue-600 hover:bg-blue-700">
              <Link href={data.ctaButton.href}>{data.ctaButton.text}</Link>
            </Button>
          </div>
          <div className="flex justify-center lg:justify-end">
            <Image
              src={data.image || "/images/placeholder.svg"}
              alt="Educator teaching"
              width={300}
              height={400}
              className="rounded-lg object-cover"
            />
          </div>
        </div>
      </div>
    </section>
  );
}
