"use client";

import CustomAlertDialog from "@/components/shared/CustomAlertDialog";
import CustomDialog from "@/components/shared/CustomDialog";
import { CustomDropdown } from "@/components/shared/Dropdown";
import { Button } from "@/components/ui/button";
import {
  DropdownMenuItem,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import {
  deleteStudent,
  StudentWithUserAndSchool,
} from "@/lib/server/action/students";
import { ColumnDef } from "@tanstack/react-table";
import { EllipsisVertical } from "lucide-react";
import { toast } from "sonner";
import ViewStudent from "../_components/view/ViewStudent";
import { Avatar, AvatarImage } from "@/components/ui/avatar";
import { StudentForm } from "@/components/form/StudentForm";

export const columns: ColumnDef<StudentWithUserAndSchool>[] = [
  {
    accessorKey: "name",
    header: () => <div className="ml-2">Name</div>,
    cell: ({ row }) => {
      const image = row.original.user.avatar as string;
      return (
        <div className="ml-2 flex gap-3 items-center">
          <Avatar>
            <AvatarImage src={image ?? "/images/avatar.jpg"} alt="user photo" />
            {/* <AvatarFallback>{}</AvatarFallback> */}
          </Avatar>
          <div className="font-medium truncate">{`${row.original.user.firstName} ${row.original.user.lastName}`}</div>
        </div>
      );
    },
  },
  {
    accessorKey: "user.email",
    header: "Email",
    cell: ({ row }) => row.original.user.email,
  },
  {
    accessorKey: "user.phone",
    header: "Phone",
    cell: ({ row }) => row.original.user.phone,
  },
  {
    accessorKey: "school.name",
    header: "School",
    cell: ({ row }) => row.original.school.name,
  },
  {
    accessorKey: "grade.name",
    header: "Grade",
    cell: ({ row }) => row.original.grade.name,
  },
  {
    accessorKey: "course.name",
    header: "Course",
    cell: ({ row }) => row.original.enrollment?.course.title,
  },
  {
    accessorKey: "user.status",
    header: "Status",
    cell: ({ row }) => (
      <div
        className={`capitalize ${
          row.original.user.status === "APPROVED"
            ? "text-green-600"
            : row.original.user.status === "PENDING"
            ? "text-amber-600"
            : "text-red-600"
        }`}
      >
        {row.original.user.status.toLowerCase()}
      </div>
    ),
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const student = row.original;

      return (
        <CustomDropdown
          trigger={
            <Button size="icon" variant="ghost">
              <EllipsisVertical />
            </Button>
          }
        >
          <DropdownMenuItem asChild>
            <CustomDialog asChild={false} trigger="View" title="User Details">
              <ViewStudent
                userPhoto={student.user.avatar}
                firstName={student.user.firstName}
                lastName={student.user.lastName}
                status={student.user.status}
                email={student.user.email}
                school={student.school.name}
                course={student.enrollment?.course.title || ""}
                grade={student.grade.name}
              />
            </CustomDialog>
          </DropdownMenuItem>
          <DropdownMenuItem asChild>
            <CustomDialog title="Update" asChild={false} trigger="Edit">
              <StudentForm
                studentData={{
                  firstName: student.user.firstName,
                  lastName: student.user.lastName,
                  email: student.user.email,
                  phone: student.user.phone || "",
                  school: student.school.id,
                  grade: student.grade.id,
                  course: student.enrollment?.course.id || "",
                }}
                studentId={student.id}
                enrollmentId={student.enrollment?.id}
              />
            </CustomDialog>
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem asChild>
            <CustomAlertDialog
              asChild={false}
              trigger="Delete"
              onConfirm={async () => {
                const res = await deleteStudent(student.id);
                if (res.success) {
                  toast.success("Student deleted");
                } else {
                  toast.error("Failed to delete student");
                }
              }}
            />
          </DropdownMenuItem>
        </CustomDropdown>
      );
    },
  },
];
