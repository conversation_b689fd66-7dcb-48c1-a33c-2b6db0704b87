import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Users, Eye, Phone, Video } from "lucide-react";

interface Meeting {
  id: string;
  title: string;
  description?: string | null;
  type: "CONFERENCE_CALL" | "VIDEO_CALL";
  status: "SCHEDULED" | "ONGOING" | "COMPLETED" | "CANCELLED";
  scheduledAt: Date;
  meetUrl?: string | null;
  participants: Array<{
    user: {
      firstName: string;
      lastName: string;
      email: string;
    };
  }>;
}

interface MeetingCardProps {
  meeting: Meeting;
  onJoin?: (meetingId: string) => void;
  onView: (meetingId: string) => void;
  onDelete?: (meetingId: string) => void;
}

export default function MeetingCard({
  meeting,
  onJoin,
  onView,
  onDelete,
}: MeetingCardProps) {
  const isOngoing = meeting.status === "ONGOING";
  const Icon = meeting.type === "CONFERENCE_CALL" ? Phone : Video;
  // const isUpcoming = meeting.status === 'SCHEDULED'

  return (
    <Card key={meeting.id} className="hover:shadow-lg transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div
              className={`w-10 h-10 rounded-full flex items-center justify-center ${
                isOngoing ? "bg-blue-100" : "bg-green-100"
              }`}
            >
              <Icon
                className={`w-5 h-5 ${
                  isOngoing ? "text-primary" : "text-green-600"
                }`}
              />
            </div>
            <div>
              <CardTitle className="text-lg">{meeting.title}</CardTitle>
              <div className="flex items-center gap-2 mt-1">
                <Badge variant={isOngoing ? "default" : "secondary"}>
                  {isOngoing ? "Ongoing" : "Upcoming"}
                </Badge>
                <span className="text-sm text-gray-500">
                  {meeting.scheduledAt.toDateString()}
                </span>
              </div>
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <p className="text-gray-600 text-sm">{meeting.description}</p>

        <div className="flex items-center gap-2 text-sm text-gray-500">
          <Users className="w-4 h-4" />
          <span>{meeting.participants.length} participants</span>
        </div>

        <div className="flex gap-2">
          <Button
            variant="outline"
            className="flex-1"
            onClick={() => onView(meeting.id)}
          >
            <Eye className="w-4 h-4 mr-2" />
            View
          </Button>
          {isOngoing ? (
            <Button onClick={() => onJoin?.(meeting.id)}>
              <Users className="w-4 h-4 mr-2" />
              Join
            </Button>
          ) : (
            <Button
              variant="destructive"
              className="flex-1"
              onClick={() => onDelete?.(meeting.id)}
            >
              <Users className="w-4 h-4 mr-2" />
              Delete
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
