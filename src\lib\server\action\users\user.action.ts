'use server'

import { comparePassword } from "@/lib/passwordHandler"
import prisma from "@/lib/prisma"
import { signIn, signOut } from "../../../../../auth"

export async function authenticateAdmin (email: string, password: string) {
  try {
    const user = await prisma.user.findUnique({
      where: { email, role: "ADMIN" },
      include: {
        adminProfile: {
          select: {
            id: true,
            password: true,
            isFirst: true,
          }
        },
      },
    })
    
    if (!user || !user.adminProfile) {
      return null
    }

    const isPasswordValid = await comparePassword(password, user.adminProfile.password)

    if (!isPasswordValid) {
      return null
    }

    return {
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      isFirst: user.adminProfile.isFirst,
      role: user.role,
      avatar: user.avatar,
      profileId: user.adminProfile.id,
    }
  } catch (error) {
    console.log(error)
    return null
  }
}

export async function authenticateLoginCode (loginCode: string) {
  try {
    const code = await prisma.code.findUnique({
      where: { code: loginCode },
      include: {
        user: {
          select: {
            id: true,
            role: true,
            email: true,
            firstName: true,
            lastName: true,
            avatar: true,
          },
        },
      },
    })
    
    if (!code || !code.user) {
      return null
    }

    let profile: { profileId: string; courseId?: string }

    if (code.user.role === "TEACHER") {
      const teacherProfile = await prisma.teacherProfile.findUnique({
        where: { userId: code.userId, user: {status: "APPROVED"} },
        select: {
          id: true,
        },
      })
      
      if (!teacherProfile) {
        return null
      }

      profile = {
        profileId: teacherProfile.id,
      }
    } else if (code.user.role === "STUDENT") {
      const studentProfile = await prisma.studentProfile.findUnique({
        where: { userId: code.userId, user: {status: "APPROVED"} },
        select: {
          id: true,
          courseId: true,
        },
      })
      
      if (!studentProfile) {
        return null
      }

      profile = {
        profileId: studentProfile.id,
        courseId: studentProfile.courseId,
      }
    } else {
      return null
    }

    return {
      id: code.userId,
      email: code.user.email,
      firstName: code.user.firstName,
      lastName: code.user.lastName,
      role: code.user.role,
      avatar: code.user.avatar,
      ...profile, // teacherId, studentId, courseId
    }
  } catch (error) {
    console.log(error)
    return null
  }
}

export async function signInWithCredentials({type, email, password, loginCode}: {type: "ep" | "lc"; email?: string; password?: string, loginCode?: string}) {
  try {
    if ((type === "ep" && (!email || !password)) || (type === "lc" && !loginCode)) {
      return {  success: false, error: "Please fill all the required fields" }
    }

    await signIn('credentials', {type, email, password, loginCode, redirect: false})
    return { success: true, message: "Login successful"}
  } catch (error) {
    console.log(error);
    return { success: false, error: "Invalid login credentials."}
  }
}

export async function logout() {
  await signOut();
}