'use server'

import { appConfig } from "@/config/app";
import prisma from "@/lib/prisma";
import { Prisma } from "@prisma/client";


export type CodeWithUserAndSchool = Prisma.CodeGetPayload<{
  include: {
    user: {
      select: {
        firstName: true,
        lastName: true,
        email: true
      }
    };
  };
}>;

export async function getAllCodes() {
  const codes = await prisma.code.findMany({
    include: {
      user: {
        select: {
          firstName: true,
          lastName: true,
          email: true
        },
      },
    },
  });

  return codes;
}

export async function getCodes({
  sessionId,
  page,
  search,
  sortby,
}: {
  sessionId: string;
  page: number;
  search?: string;
  sortby?: string;
}): Promise<{
  codes: CodeWithUserAndSchool[]; // Corrected type here
  total: number;
}> {
  const whereClause: Prisma.CodeWhereInput = {};
  const orderByClause: Prisma.CodeOrderByWithRelationInput[] = [];

  whereClause.sessionId = sessionId;

  if (search) {
    whereClause.OR = [
      { user: { firstName: { contains: search } } },
      { user: { lastName: { contains: search } } },
      { user: { email: { contains: search } } },
    ];
  }

  if (sortby) {
    switch (sortby) {
      case "name":
        orderByClause.push({ user: { firstName: "asc" } });
        break;
      case "email":
        orderByClause.push({ user: { email: "asc" } });
        break;
      case "status":
        orderByClause.push({ user: { status: "asc" } });
        break;
      default:
        orderByClause.push({ user: { firstName: "asc" } });
        break;
    }
  } else {
    orderByClause.push({ user: { firstName: "asc" } });
  }

  const queryOptions: Prisma.CodeFindManyArgs = {
    select: {
      id: true,
      code: true,
      type: true,
      expireTime: true,
      status: true,
      user: {
        select: {
          firstName: true,
          lastName: true,
          email: true
        }
      },
    },
    where: whereClause,
    orderBy: orderByClause.length > 0 ? orderByClause : [{ user: { firstName: "asc" } }],
    skip: (page - 1) * appConfig.ITEMS_PER_PAGE,
    take: appConfig.ITEMS_PER_PAGE,
  };

  try {
    const codes = await prisma.code.findMany(queryOptions) as CodeWithUserAndSchool[];

    const total = await prisma.code.count({
      where: whereClause,
    });

    return { codes, total };
  } catch (error) {
    console.error("Error fetching codes:", error);
    throw new Error("Failed to fetch codes.");
  }
}