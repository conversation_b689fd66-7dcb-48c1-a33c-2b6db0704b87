"use client";

import CustomAlertDialog from "@/components/shared/CustomAlertDialog";
import CustomDialog from "@/components/shared/CustomDialog";
import { CustomDropdown } from "@/components/shared/Dropdown";
import { Button } from "@/components/ui/button";
import {
  DropdownMenuItem,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import {
  deleteTeacher,
  TeacherWithUserAndSchool,
} from "@/lib/server/action/teachers";
import { ColumnDef } from "@tanstack/react-table";
import { EllipsisVertical } from "lucide-react";
import { toast } from "sonner";
import ViewTeacher from "../_components/view/ViewTeacher";
import { Avatar, AvatarImage } from "@/components/ui/avatar";
import TeacherForm from "@/components/form/TeacherForm";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";

export const columns: ColumnDef<TeacherWithUserAndSchool>[] = [
  {
    accessorKey: "name",
    header: () => <div className="ml-2">Name</div>,
    cell: ({ row }) => {
      const image = row.original.user.avatar as string;
      return (
        <div className="ml-2 flex gap-3 items-center">
          <Avatar>
            <AvatarImage src={image ?? "/images/avatar.jpg"} alt="user photo" />
            {/* <AvatarFallback>{}</AvatarFallback> */}
          </Avatar>
          <div className="font-medium truncate">{`${row.original.user.firstName} ${row.original.user.lastName}`}</div>
        </div>
      );
    },
  },
  {
    accessorKey: "user.email",
    header: "Email",
    cell: ({ row }) => row.original.user.email,
  },
  {
    accessorKey: "user.phone",
    header: "Phone",
    cell: ({ row }) => row.original.user.phone,
  },
  {
    accessorKey: "school.name",
    header: "School",
    cell: ({ row }) => row.original.school.name,
  },
  {
    accessorKey: "user.status",
    header: "Status",
    cell: ({ row }) => (
      <div
        className={`capitalize ${
          row.original.user.status === "APPROVED"
            ? "text-green-600"
            : row.original.user.status === "PENDING"
            ? "text-amber-600"
            : "text-red-600"
        }`}
      >
        {row.original.user.status.toLowerCase()}
      </div>
    ),
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const teacher = row.original;

      return (
        <CustomDropdown
          trigger={
            <Button size="icon" variant="ghost">
              <EllipsisVertical />
            </Button>
          }
        >
          <DropdownMenuItem asChild>
            <CustomDialog asChild={false} trigger="View" title="User Details">
              <ViewTeacher
                userPhoto={teacher.user.avatar}
                firstName={teacher.user.firstName}
                lastName={teacher.user.lastName}
                status={teacher.user.status}
                email={teacher.user.email}
                school={teacher.school.name}
                course={teacher.courseAssignments[0]?.course.title || ""}
              />
            </CustomDialog>
          </DropdownMenuItem>
          <DropdownMenuItem asChild>
            <CustomDialog title="Update" asChild={false} trigger="Edit">
              <TeacherForm
                teacherData={{
                  firstName: teacher.user.firstName,
                  lastName: teacher.user.lastName,
                  email: teacher.user.email,
                  phone: teacher.user.phone || "",
                  school: teacher.school.id,
                }}
                teacherId={teacher.id}
              />
            </CustomDialog>
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          {teacher.courseAssignments.length > 0 &&
          teacher.courseAssignments[0].role === "PRIMARY" ? (
            <DropdownMenuItem asChild>
              <Tooltip>
                <TooltipTrigger className="cursor-not-allowed text-sm text-start w-full px-2 py-1">
                  Delete
                </TooltipTrigger>
                <TooltipContent>
                  <p>
                    Teacher already assigned as a primary teacher to a course.
                    Unassign teacher from course before deleting.
                  </p>
                </TooltipContent>
              </Tooltip>
            </DropdownMenuItem>
          ) : (
            <DropdownMenuItem asChild>
              <CustomAlertDialog
                asChild={false}
                trigger="Delete"
                onConfirm={async () => {
                  const res = await deleteTeacher(teacher.id);
                  if (res.success) {
                    toast.success("Teacher deleted");
                  } else {
                    toast.error("Failed to delete teacher");
                  }
                }}
              />
            </DropdownMenuItem>
          )}
        </CustomDropdown>
      );
    },
  },
];
