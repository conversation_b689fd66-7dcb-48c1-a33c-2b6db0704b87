'use server'

import prisma from "@/lib/prisma";
import { Prisma } from "@prisma/client";


export type GradeWithCourses = Prisma.GradeGetPayload<{
  include: {
    courses: {
      select: {
        id: true,
        title: true
      }
    },
    _count: {
      select: {
        courses: true
      }
    }
  };
}>;

export async function getGrades({ sessionId }: { sessionId: string }) {
  try {
    const grades = await prisma.grade.findMany({
      where: { sessionId },
      include: {
        courses: {
          select: {
            id: true,
            title: true
          }
        },
        _count: {
          select: {
            courses: true
          }
        }
      },
      orderBy: { order: 'asc' }
    });

    const total = await prisma.grade.count();

    return { grades, total };
  } catch (error) {
    console.error("Error fetching grades:", error);
    throw new Error("Failed to fetch grades.");
  }
}

export async function getGrade(gradeId: string) {
  try {
    const grade = await prisma.grade.findUnique({
      where: { id: gradeId },
      include: {
        courses: true
      },
    });

    return grade;
  } catch (error) {
    console.error("Error fetching grade:", error);
    throw new Error("Failed to fetch grade.");
  }
}

export async function getGradeOptions(sessionId: string) {
  try {
    const grades = await prisma.grade.findMany({
      where: { sessionId },
      select: {
        id: true,
        name: true
      },
      orderBy: { order: 'asc' }
    })
    
    return grades.map(grade => ({ label: grade.name, value: grade.id }))
  } catch (error) {
    console.log(error)
    return []
  }
}