'use server'

import prisma from "@/lib/prisma"
import { revalidatePath } from "next/cache"
import { TModuleForm } from "./modules.schema"

export async function createModule(courseId: string, data: TModuleForm) {
  try {
    const course = await prisma.course.findUnique({ where: { id: courseId } })
    
    if (!course) {
      return { success: false, error: 'Course not found' }
    }
    
    await prisma.module.create({
      data: {
        title: data.title,
        description: data.description,
        order: await getNextModuleOrder(courseId),
        course: { connect: { id: courseId } }
      }
    })
    
    revalidatePath(`/admin/courses/${courseId}`)
    return { success: true, message: 'Module created' }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to create module' }
  }
}

export async function updateModule(moduleId: string, data: Partial<TModuleForm>) {
  try {
    const modules = await prisma.module.update({
      where: { id: moduleId },
      data,
      include: { course: {
        select: {
          id: true
        }
      }}
    })
    
    revalidatePath(`/admin/courses/${modules.course.id}`)
    return { success: true, message: 'Module updated' }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to update module' }
  }
}

export async function deleteModule(moduleId: string) {
  try {
    const modules = await prisma.module.findUnique({
      where: { id: moduleId },
      include: { course: true }
    })
    
    if (!modules) {
      return { success: false, error: 'Module not found' }
    }
    
    await prisma.module.delete({
      where: { id: moduleId }
    })
    
    revalidatePath(`/admin/courses/${modules.course.id}`)
    return { success: true }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to delete module' }
  }
}

async function getNextModuleOrder(courseId: string): Promise<number> {
  const lastModule = await prisma.module.findFirst({
    where: { courseId },
    orderBy: { order: 'desc' }
  })
  return (lastModule?.order || 0) + 1
}

export async function changeModuleOrder(moduleId: string, newOrder: number) {
  try {
    await prisma.module.update({
      where: { id: moduleId },
      data: { order: newOrder }
    })
    
    return { success: true }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to change module order' }
  }
}
