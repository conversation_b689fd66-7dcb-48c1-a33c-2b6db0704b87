'use server'

import prisma from "@/lib/prisma"
import { revalidatePath } from "next/cache"
import { trackModuleProgress } from "../../progress-tracking/progress-tracking.action";

export async function trackLessonVideoProgress({lessonId, studentId, videoDuration, progress, isCompleted }: {lessonId: string, studentId: string, videoDuration: string, progress: number, isCompleted: boolean}) {
  try {
    const lessonProgress = await prisma.lessonVideoProgress.upsert({
      where: {
        studentId_lessonId: {
          studentId,
          lessonId
        }
      },
      update: {
        progress: Math.max(progress, 0),
        lastAccessed: new Date(),
        completedAt: progress >= 100 ? new Date() : null,
        isCompleted
      },
      create: {
        lessonId,
        studentId,
        videoDuration,
        progress: Math.max(progress, 0),
        lastAccessed: new Date(),
        completedAt: progress >= 100 ? new Date() : null,
        hasWatched: true,
      },
      select: { hasWatched: true, lesson: { select: { moduleId: true } } }
    })

    if (!lessonProgress.hasWatched) {
      await trackModuleProgress(studentId, lessonProgress.lesson.moduleId, 50);
    }

    revalidatePath('/student/dashboard/modules')
    
    return { success: true, lessonProgress }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to track progress' }
  }
}