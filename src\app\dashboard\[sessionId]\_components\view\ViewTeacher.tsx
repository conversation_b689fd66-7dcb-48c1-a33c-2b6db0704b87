import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";
import Image from "next/image";
import OtherDetails from "../shared/OtherDetails";
import { UserStatus } from "@prisma/client";

type Props = {
  userPhoto?: string | null;
  firstName: string;
  lastName: string;
  status: UserStatus;
  email: string;
  school: string;
  course: string;
};

export default function ViewTeacher({
  userPhoto,
  firstName,
  lastName,
  status,
  email,
  school,
  course,
}: Props) {
  return (
    <div className="flex flex-col gap-y-4">
      <div className="flex flex-col items-center">
        <Image
          src={userPhoto ?? "/images/avatar.jpg"}
          alt="photo"
          width={60}
          height={60}
          className="rounded-full"
        />
        <h2 className="text-center font-medium text-base">{`${firstName} ${lastName}`}</h2>
        <p className="text-center font-medium text-xs text-muted-foreground">
          {email}
        </p>
        <p
          className={cn(
            "text-center font-medium text-xs text-muted-foreground mt-2",
            status === "APPROVED" ? "text-green-500" : "text-orange-500"
          )}
        >
          {status}
        </p>
      </div>
      <Separator />
      <div>
        <OtherDetails name="School" value={school} />
        <OtherDetails name="Course" value={course} />
      </div>
    </div>
  );
}
