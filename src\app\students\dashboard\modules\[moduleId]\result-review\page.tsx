import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from "lucide-react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import <PERSON> from "next/link";

interface QuizQuestion {
  id: number;
  question: string;
  type: "True/False" | "Multiple Choice";
  isCorrect: boolean;
  points: number;
  maxPoints: number;
  userAnswer?: string;
  correctAnswer?: string;
}

const quizData: QuizQuestion[] = [
  {
    id: 1,
    question: "<PERSON> was the first President of the United States.",
    type: "True/False",
    isCorrect: true,
    points: 1,
    maxPoints: 1,
  },
  {
    id: 2,
    question: "The Great Wall of China was built primarily for defense.",
    type: "True/False",
    isCorrect: false,
    points: 1,
    maxPoints: 1,
  },
  {
    id: 3,
    question: "The capital of Australia is Sydney.",
    type: "True/False",
    isCorrect: false,
    points: 1,
    maxPoints: 1,
  },
  {
    id: 4,
    question: "World War II ended in 1945.",
    type: "True/False",
    isCorrect: true,
    points: 1,
    maxPoints: 1,
  },
  {
    id: 5,
    question: "The ancient Egyptians used hieroglyphics as a form of writing.",
    type: "True/False",
    isCorrect: true,
    points: 1,
    maxPoints: 1,
  },
  {
    id: 6,
    question: "What is the main purpose of the United Nations?",
    type: "Multiple Choice",
    isCorrect: false,
    points: 1,
    maxPoints: 1,
  },
  {
    id: 7,
    question: "Which event marked the beginning of the Great Depression?",
    type: "Multiple Choice",
    isCorrect: true,
    points: 1,
    maxPoints: 1,
  },
  {
    id: 8,
    question: "What was the main outcome of the Treaty of Versailles?",
    type: "Multiple Choice",
    isCorrect: false,
    points: 1,
    maxPoints: 1,
  },
  {
    id: 9,
    question: "Who is known as the 'Father of History'?",
    type: "Multiple Choice",
    isCorrect: true,
    points: 1,
    maxPoints: 1,
  },
  {
    id: 10,
    question:
      "Which empire was known for its road system and communication networks?",
    type: "Multiple Choice",
    isCorrect: true,
    points: 1,
    maxPoints: 1,
  },
];

export default async function QuizReview({
  searchParams,
  params,
}: {
  searchParams: Promise<{ [key: string]: string | undefined }>;
  params: Promise<{ studentId: string; courseId: string }>;
}) {
  const { studentId, courseId } = await params;
  const { assessment } = await searchParams;
  const totalQuestions = quizData.length;

  const correctAnswers = quizData.filter((q) => q.isCorrect).length;
  const accuracy = Math.round((correctAnswers / totalQuestions) * 100);

  console.log(assessment);

  const getQuestionTypeColor = (type: string) => {
    switch (type) {
      case "True/False":
        return "bg-blue-100 text-blue-800";
      case "Multiple Choice":
        return "bg-purple-100 text-purple-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-gray-50 min-h-screen">
      <Button variant="ghost" asChild className="mb-4">
        <Link href={`/students/${studentId}/courses/${courseId}`}>
          <ArrowLeft className="w-4 h-4" />
          Back to Course
        </Link>
      </Button>
      <Card className="mb-6">
        <CardHeader className="pb-4">
          <div className="flex justify-between items-start">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 mb-2">
                Medium Level General Knowledge Quiz
              </h1>
              <p className="text-sm text-gray-600">
                Finished February 6, 2023 @ 11:37 AM
              </p>
            </div>
            <div className="flex items-center gap-6">
              <div className="text-center">
                <div className="flex items-center justify-center gap-2 mb-1">
                  <div className="w-8 h-8 rounded-full bg-green-500 flex items-center justify-center">
                    <span className="text-white text-sm font-bold">
                      {accuracy}%
                    </span>
                  </div>
                </div>
                <p className="text-xs text-gray-600">Accuracy</p>
              </div>
              <div className="text-center">
                <div className="flex items-center gap-1 mb-1">
                  <span className="text-lg font-semibold text-gray-900">
                    {correctAnswers}/{totalQuestions}
                  </span>
                  <div className="w-4 h-4 rounded-full border-2 border-gray-300 flex items-center justify-center">
                    <div className="w-2 h-2 rounded-full bg-gray-400"></div>
                  </div>
                </div>
                <p className="text-xs text-gray-600">Correct Answers</p>
              </div>
            </div>
          </div>
        </CardHeader>
      </Card>

      <Card>
        <CardHeader>
          <h2 className="text-xl font-semibold text-gray-900">Quiz Summary:</h2>
        </CardHeader>
        <CardContent className="space-y-4">
          {quizData.map((question) => (
            <div key={question.id} className="border rounded-lg p-4 bg-white">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-start gap-3 mb-2">
                    <span className="font-semibold text-gray-900">
                      Q {question.id}.
                    </span>
                    <div className="flex-1">
                      <p className="text-gray-900 mb-2">{question.question}</p>
                      <div className="flex items-center gap-3 text-sm">
                        <Badge
                          variant="outline"
                          className={getQuestionTypeColor(question.type)}
                        >
                          Type: {question.type}
                        </Badge>
                        <div className="flex items-center gap-1">
                          {question.isCorrect ? (
                            <Check className="w-4 h-4 text-green-600" />
                          ) : (
                            <X className="w-4 h-4 text-red-600" />
                          )}
                          <span
                            className={
                              question.isCorrect
                                ? "text-green-600"
                                : "text-red-600"
                            }
                          >
                            {question.isCorrect ? "Correct" : "Incorrect"}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </CardContent>
      </Card>
    </div>
  );
}
