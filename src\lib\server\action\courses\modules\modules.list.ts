'use server'

import prisma from "@/lib/prisma"
import { Prisma } from "@prisma/client";

export type ModulesWithDetails = {
  id: string;
  title: string;
  description: string | null;
  questions: number;
  totalPoints: number;
  created: string;
  attempts: number;
};

export async function getModules({ courseId, search }: { courseId: string, search?: string }) {
  const whereClause: Prisma.ModuleWhereInput = {};

  whereClause.courseId = courseId;

  if (search) {
    whereClause.OR = [
      { title: { contains: search } },
      { description: { contains: search } },
    ];
  }

  try {
    const modules = await prisma.module.findMany({
      where: whereClause,
      orderBy: { order: 'asc' },
      select: {
        id: true,
        title: true,
        description: true,
        createdAt: true,
        assessment: {
          select: {
            _count: {
              select: {
                attempts: true,
                questions: true
              }
            },
            questions: {
              select: {
                points: true
              }
            }
          },
        }
      }
    })

    const moduleData = modules.map(module => ({
      id: module.id,
      title: module.title,
      description: module.description,
      questions: module.assessment?._count.questions ?? 0,
      totalPoints: module.assessment?.questions.reduce((sum, q) => sum + q.points, 0) ?? 0,
      created: module.createdAt.toDateString(),
      attempts: module.assessment?._count.attempts ?? 0,
    }))
    
    return moduleData
  } catch (error) {
    console.log(error)
    throw new Error("Failed to fetch modules.")
  }
}

export async function getModuleOverviewCounts (courseId: string) {
  try {
    // const counts = await prisma.module.findMany({
    //   where: { courseId },
    //   select: {
    //     assessment: {
    //       select: {
    //         _count: true
    //       }
    //     }
    //   }
    // })

    const [totalModules, totalAttempts] = await Promise.all([prisma.module.count({
      where: { courseId }
    }), prisma.assessmentAttempt.count({
      where: { assessment: { module: { courseId } } }
    })])
    // const totalAttempts = counts.reduce((acc, module) => acc + (module.assessment?._count.attempts ?? 0), 0)
    
    return { totalModules, totalAttempts }
  } catch (error) {
    console.log(error)
    throw new Error("Failed to fetch modules.")
  }
}

export async function getModule(moduleId: string) {
  try {
    const moduleD = await prisma.module.findUnique({
      where: { id: moduleId },
      select: {
        id: true,
        title: true,
        description: true,
        course: {
          select: {
            title: true,
            _count: {
              select: {
                enrollments: true
              }
            },
            teacherAssignments: {
              where: { role: "PRIMARY" },
              select: {
                teacher: {
                  select: {
                    user: {
                      select: {
                        firstName: true,
                        lastName: true
                      }
                    }
                  }
                }
              }
            },
            grade: {
              select: {
                name: true
              }
            }
          }
        },
        assessment: {
          select: {
            _count: {
              select: {
                questions: true
              }
            }
          }
        }
      }
    })

    if (!moduleD) return null

    const moduleData = {
      id: moduleD.id,
      title: moduleD.title,
      description: moduleD.description,
      course: moduleD.course.title,
      grade: moduleD.course.grade.name,
      teacher: moduleD.course.teacherAssignments[0].teacher.user.firstName + " " + moduleD?.course?.teacherAssignments[0].teacher.user.lastName,
      count: {
        questions: moduleD.assessment?._count.questions ?? 0,
        students: moduleD.course._count.enrollments ?? 0
      }
    }
    
    return moduleData
  } catch (error) {
    console.log(error)
    return null
  }
}

// Student
export async function getStudentModules(courseId: string, studentId: string) {
  try {
    const modules = await prisma.module.findMany({
      where: { courseId },
      orderBy: { order: 'asc' },
      select: {
        id: true,
        title: true,
        description: true,
        createdAt: true,
        order: true,
        assessment: {
          select: {
            id: true,
            passingScore: true,
            maxStudentQuestions: true,
            _count: {
              select: {
                attempts: true,
                questions: true
              }
            },
            questions: {
              select: {
                points: true
              }
            },
            attempts: {
              where: { studentId },
              select: {
                attemptNumber: true,
                status: true,
                score: true,
                totalPoints: true,
                passed: true,
                startedAt: true,
                completedAt: true
              },
              orderBy: [
                { score: 'desc' },
                { attemptNumber: 'desc' }
              ],
              take: 1
            }
          },
        },
        progress: {
          where: { studentId },
          select: {
            progress: true,
            completedAt: true
          },
          orderBy: { updatedAt: 'desc' },
          take: 1
        },
        lesson: {
          select: {
            id: true,
            title: true,
            description: true,
            type: true,
            videoUrl: true,
            docUrl: true,
            duration: true,
            progress: {
              where: { studentId },
              select: {
                progress: true,
                completedAt: true,
                isCompleted: true,
                hasWatched: true
              },
              orderBy: { updatedAt: 'desc' },
              take: 1
            },
          }
        }
      }
    })

    const moduleData = modules.map(module => ({
      id: module.id,
      title: module.title,
      description: module.description,
      lesson: {
        id: module.lesson?.id ?? null,
        title: module.lesson?.title ?? null,
        description: module.lesson?.description ?? null,
        type: module.lesson?.type ?? null,
        url: module.lesson?.type === 'VIDEO' ? module.lesson?.videoUrl : module.lesson?.docUrl,
        video: module.lesson?.type === 'VIDEO' ? {
          url: module.lesson.videoUrl,
          duration: module.lesson.duration || "00:00",
          progress: module.lesson.progress[0]?.progress ?? 0,
          completedAt: module.lesson.progress[0]?.completedAt ?? null,
          isCompleted: module.lesson.progress[0]?.isCompleted ?? false,
          hasWatched: module.lesson.progress[0]?.hasWatched ?? false
        } : null,
        doc: module.lesson?.type === 'DOC' ? module.lesson?.docUrl : null,
      },
      totalPoints: module.assessment?.questions.reduce((sum, q) => sum + q.points, 0) ?? 0,
      created: module.createdAt.toDateString(),
      assessment: {
        id: module.assessment?.id ?? null,
        passingScore: module.assessment?.passingScore ?? 0,
        maxStudentQuestions: module.assessment?.maxStudentQuestions ?? 0,
        attempts: module.assessment?._count.attempts ?? 0,
        completedAt: module.assessment?.attempts[0]?.completedAt ?? null,
        status: module.assessment?.attempts[0]?.status,
        questionCount: module.assessment?._count.questions ?? 0,
        // score: module.assessment?.attempts[0].score ?? 0,
        // totalPoints: module.assessment?.attempts[0]?.totalPoints ?? 0,
        passed: module.assessment?.attempts[0]?.passed ?? false,
      },
      bestAssessment: module.assessment?.attempts && module.assessment.attempts.length > 0 ? module.assessment.attempts[0] : null,
      progress: module.progress[0]?.progress ?? 0,
      completedAt: module.progress[0]?.completedAt ?? null
    }))
    
    return moduleData
  } catch (error) {
    console.log(error)
    throw new Error("Failed to fetch modules.")
  }
}

export type StudentModule = Awaited<ReturnType<typeof getStudentModules>>[number];