"use client";

import type React from "react";

import { useMemo } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  BarChart2,
  BookOpen,
  File,
  Laptop,
  LogOut,
  Rows4,
  ScrollText,
} from "lucide-react";

import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
} from "@/components/ui/sidebar";
import { Separator } from "@/components/ui/separator";
import Logo from "@/components/shared/logo";
import { logout } from "@/lib/server/action/users/user.action";

interface SidebarLink {
  title: string;
  url: string;
  icon: React.ElementType;
}

// Define sidebar links as a constant outside the component to prevent re-creation on each render
const SIDEBAR_LINKS: SidebarLink[] = [
  {
    title: "Dashboard",
    url: "/students/dashboard",
    icon: BarChart2,
  },
  // {
  //   title: "Grade and Course",
  //   url: "/students/dashboard/grades",
  //   icon: GraduationCap,
  // },
  {
    title: "Files",
    url: "/students/dashboard/files",
    icon: File,
  },
  {
    title: "Modules",
    url: "/students/dashboard/modules",
    icon: BookOpen,
  },
  {
    title: "Activities",
    url: "/students/dashboard/activities",
    icon: ScrollText,
  },
  {
    title: "Virtual Classroom",
    url: "/students/dashboard/classroom",
    icon: Laptop,
  },
  {
    title: "My Certificate",
    url: "/students/dashboard/certificate",
    icon: Rows4,
  },
];

function AppSidebar() {
  const pathname = usePathname();

  // Memoize the active link to prevent unnecessary re-renders
  const { activeLinks } = useMemo(() => {
    const active = new Set<string>();

    // Check which links should be active
    SIDEBAR_LINKS.forEach((link) => {
      // Check if the current path matches the main link
      if (pathname === link.url) {
        active.add(link.url);
      }
    });

    return { activeLinks: active };
  }, [pathname]);

  return (
    <Sidebar collapsible="icon" className="overflow-hidden">
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton size="lg" asChild className="gap-0">
              <Logo />
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel>Platform</SidebarGroupLabel>
          <SidebarMenu>
            {/* Render links without dropdowns */}
            {SIDEBAR_LINKS.map((item) => (
              <SidebarMenuItem key={item.title}>
                <SidebarMenuButton
                  tooltip={item.title}
                  className="h-10 px-2.5"
                  asChild
                  isActive={activeLinks.has(item.url)}
                >
                  <Link href={item.url}>
                    <item.icon className="h-4 w-4" />
                    <span>{item.title}</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
            ))}

            <Separator className="my-4" />

            {/* Sign out button */}
            <SidebarMenuItem>
              <SidebarMenuButton
                tooltip="Sign out"
                className="h-10 px-2.5 text-red-500 hover:bg-red-500/10 hover:text-red-600"
                asChild
              >
                <button onClick={async () => await logout()}>
                  <LogOut className="h-4 w-4" />
                  <span>Sign out</span>
                </button>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarGroup>
      </SidebarContent>
      <SidebarRail />
    </Sidebar>
  );
}

export default AppSidebar;
