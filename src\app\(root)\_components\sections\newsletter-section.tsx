import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

interface NewsletterSectionProps {
  data: {
    title: string;
    placeholder: string;
    buttonText: string;
  };
}

export default function NewsletterSection({ data }: NewsletterSectionProps) {
  return (
    <section className="w-full py-16 bg-blue-600">
      <div className="container mx-auto px-4">
        <div className="text-center space-y-6">
          <h2 className="text-3xl font-bold text-white lg:text-4xl">
            {data.title}
          </h2>
          <div className="max-w-md mx-auto flex gap-2">
            <Input
              type="email"
              placeholder={data.placeholder}
              className="bg-white border-white"
            />
            <Button className="bg-gray-800 hover:bg-gray-900 text-white">
              {data.buttonText}
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}
