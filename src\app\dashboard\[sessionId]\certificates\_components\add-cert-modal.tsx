"use client";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { createTemplate } from "@/lib/server/action/certificates/templates";
import { TTemplateForm } from "@/lib/server/action/certificates/templates/templates.schema";
import { ImageIcon, Loader, Upload } from "lucide-react";
import { useParams } from "next/navigation";
import { useState } from "react";
import { toast } from "sonner";

export default function AddCertModal() {
  // get session id from url
  const params = useParams<{ sessionId: string }>();
  if (!params.sessionId) throw new Error("Session ID is required");

  const [isUploading, setIsUploading] = useState(false);
  const [preview, setPreview] = useState<File | null>(null);
  const [formData, setFormData] = useState<TTemplateForm>({
    name: "",
    templateUrl: "",
  });

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setPreview(file);
      // Handle upload and set formData.templateUrl
      setFormData((prev) => ({ ...prev, templateUrl: file.name }));
    }
  };

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    try {
      setIsUploading(true);
      const res = await createTemplate(params.sessionId, {
        ...formData,
        name: preview?.name || "",
      });
      if (res.success) {
        toast.success(res.message);
      } else {
        toast.error(res.error);
      }
    } catch (error) {
      console.error(error);
      toast.error("An error occurred while uploading the file");
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="space-y-4">
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
          {preview ? (
            <div className="space-y-2">
              <ImageIcon className="w-8 h-8 mx-auto text-green-600" />
              <p className="text-sm text-green-600">{preview.name}</p>
            </div>
          ) : (
            <div className="space-y-2">
              <Upload className="w-8 h-8 mx-auto text-gray-400" />
              <p className="text-sm text-gray-600">
                Upload certificate template
              </p>
            </div>
          )}
        </div>
        <Input
          type="file"
          accept="image/*"
          onChange={handleImageUpload}
          className="cursor-pointer"
          required
        />
      </div>
      <Button type="submit" disabled={isUploading}>
        {isUploading ? (
          <>
            <Loader /> Uploading
          </>
        ) : (
          "Upload"
        )}
      </Button>
    </form>
  );
}
