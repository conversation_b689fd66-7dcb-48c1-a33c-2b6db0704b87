'use server'

import { auth } from "../../../../../../auth";
import prisma from "@/lib/prisma";
import { TMeetingForm } from "@/lib/server/action/courses/virtual-classrooms/virtual-classrooms.schema";
import { scheduleMeeting } from "@/lib/server/google/google-meet";
import { revalidatePath } from "next/cache";

export async function createMeeting(courseId: string, data: TMeetingForm) {
  const { title, description, type, scheduledAt } = data

  try {
    const session = await auth()

    if (!session) {
      return { success: false, error: 'Unauthorized' }
    }

    const startTime = new Date(scheduledAt)
    const endTime = new Date(startTime.getTime() + 60 * 60 * 1000) // 1 hour duration

    // Create Google Meet
    const meetData = await scheduleMeeting({
      title,
      description,
      startTime,
      endTime,
      // attendees: participantEmails
    })

    // Create meeting in database
    await prisma.meeting.create({
      data: {
        title,
        description,
        type,
        scheduledAt: startTime,
        host: { connect: { id: courseId } },
        meetUrl: meetData.meetUrl,
        meetId: meetData.meetId,
        // participants: {
        //   create: participantEmails?.map((email: string) => ({
        //     user: {
        //       connect: { email }
        //     }
        //   })) || []
        // }
      },
    })

    revalidatePath(`/dashboard/courses/${courseId}`)

    return { success: true, message: 'Meeting created' }
  } catch (error) {
    console.error('Error creating meeting:', error)
    return { success: false, error: 'Failed to create meeting' }
  }
}

export async function joinMeeting(meetingId: string) {
  const session = await auth()

  if (!session) {
    return { success: false, error: 'Unauthorized' }
  }

  try {
    const meeting = await prisma.meeting.findUnique({
      where: { id: meetingId },
      include: { participants: true }
    })

    if (!meeting) {
      return { success: false, error: 'Meeting not found' }
    }

    // Update participant join time
    await prisma.meetingParticipant.upsert({
      where: {
        userId_meetingId: {
          userId: session.user.id,
          meetingId: meeting.id
        }
      },
      update: {
        joinedAt: new Date()
      },
      create: {
        userId: session.user.id,
        meetingId: meeting.id,
        joinedAt: new Date()
      }
    })

    // Update meeting status to ongoing if it's the first participant
    if (meeting.status === 'SCHEDULED') {
      await prisma.meeting.update({
        where: { id: meeting.id },
        data: {
          status: 'ONGOING',
          startedAt: new Date()
        }
      })
    }

    return { success: true, meetUrl: meeting.meetUrl }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to join meeting' }
  }
}

export async function instantMeeting (courseId: string) {
  const session = await auth()

  if (!session) {
    return { success: false, error: 'Unauthorized' }
  }

  const name = session.user.firstName + ' ' + session.user.lastName

  try {
    const now = new Date()
    const endTime = new Date(now.getTime() + 60 * 60 * 1000) // 1 hour duration

    // Create Google Meet
    const meetData = await scheduleMeeting({
      title: 'Instant Meeting',
      description: 'Instant meeting started by ' + name,
      startTime: now,
      endTime
    })

    // Create meeting in database
    const meeting = await prisma.meeting.create({
      data: {
        title: 'Instant Meeting',
        description: 'Instant meeting',
        type: 'CONFERENCE_CALL',
        status: 'ONGOING',
        scheduledAt: now,
        startedAt: now,
        host: { connect: { id: courseId } },
        meetUrl: meetData.meetUrl,
        meetId: meetData.meetId
      }
    })

    revalidatePath(`/dashboard/courses/${courseId}`)

    return { success: true, meetUrl: meeting.meetUrl, meetId: meeting.id }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to create instant meeting' }
  }
}

export async function deleteMeeting (meetingId: string) {
  try {
    const meeting = await prisma.meeting.findUnique({
      where: { id: meetingId },
      include: { host: {
        select: {
          id: true
        }
      } }
    })

    if (!meeting) {
      return { success: false, error: 'Meeting not found' }
    }

    // Delete Google Meet
    await deleteMeeting(meeting.meetId!)

    // Delete meeting from database
    await prisma.meeting.delete({
      where: { id: meeting.id }
    })

    revalidatePath(`/dashboard/courses/${meeting.host.id}`)

    return { success: true, message: 'Meeting deleted' }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to delete meeting' }
  }
}