/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { useState } from "react";
import VideoPlayer from "./VideoPlayer";
// import ResourceList from './ResourceList'
import AssessmentCard from "./cards/AssessmentCard";
import { toast } from "sonner";
import { trackModuleProgress } from "@/lib/server/action/students/progress-tracking/progress-tracking.action";

interface ModuleListProps {
  modules: any[];
  studentId: string;
  courseId: string;
}

export default function ModuleList({ modules, studentId }: ModuleListProps) {
  const [activeModule, setActiveModule] = useState<string | null>(null);

  const handleModuleProgress = async (moduleId: string, progress: number) => {
    const result = await trackModuleProgress(studentId, moduleId, progress);
    if (result.success) {
      toast.success("Progress updated");
    }
  };

  return (
    <div className="space-y-6">
      {modules.map((module, index) => {
        const isActive = activeModule === module.id;
        const progress = module.progress?.[0]?.progress || 0;
        const isCompleted = progress >= 100;
        const isLocked =
          index > 0 && (modules[index - 1].progress?.[0]?.progress || 0) < 100;

        return (
          <div
            key={module.id}
            className="bg-white rounded-lg shadow-lg overflow-hidden"
          >
            <div
              className={`p-6 cursor-pointer transition-colors ${
                isLocked ? "bg-gray-100" : "hover:bg-gray-50"
              }`}
              onClick={() =>
                !isLocked && setActiveModule(isActive ? null : module.id)
              }
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div
                    className={`w-8 h-8 rounded-full flex items-center justify-center text-white font-semibold ${
                      isCompleted
                        ? "bg-green-500"
                        : isLocked
                        ? "bg-gray-400"
                        : "bg-blue-500"
                    }`}
                  >
                    {isCompleted ? "✓" : index + 1}
                  </div>

                  <div>
                    <h3
                      className={`text-xl font-semibold ${
                        isLocked ? "text-gray-400" : ""
                      }`}
                    >
                      {module.name}
                    </h3>
                    {module.description && (
                      <p className="text-gray-600 mt-1">{module.description}</p>
                    )}
                  </div>
                </div>

                <div className="flex items-center space-x-4">
                  <div className="text-right">
                    <div className="text-sm text-gray-500">Progress</div>
                    <div className="font-semibold">{Math.round(progress)}%</div>
                  </div>

                  <div className="w-32 bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${progress}%` }}
                    />
                  </div>

                  {isLocked && <div className="text-gray-400">🔒</div>}
                </div>
              </div>
            </div>

            {isActive && !isLocked && (
              <div className="border-t">
                <div className="p-6 space-y-6">
                  {/* Video Resources */}
                  <div>
                    <h4 className="text-lg font-semibold mb-3">Videos</h4>
                    <div className="space-y-3">
                      {module.resources
                        .filter((r: any) => r.type === "VIDEO")
                        .map((resource: any) => (
                          <VideoPlayer
                            key={resource.id}
                            resource={resource}
                            onProgress={(progress) =>
                              handleModuleProgress(module.id, progress)
                            }
                          />
                        ))}
                    </div>
                  </div>

                  {/* Other Resources */}
                  {/* <ResourceList 
                    resources={module.resources.filter((r: any) => r.type !== 'VIDEO')}
                    onResourceAccess={() => handleModuleProgress(module.id, progress + 10)}
                  /> */}

                  {/* Assessments */}
                  {module.assessments.length > 0 && (
                    <div>
                      <h4 className="text-lg font-semibold mb-3">
                        Assessments
                      </h4>
                      <div className="space-y-3">
                        {module.assessments.map((assessment: any) => (
                          <AssessmentCard
                            key={assessment.id}
                            assessment={assessment}
                            studentId={studentId}
                            moduleId={module.id}
                          />
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
}
