'use server'

import { appConfig } from "@/config/app";
import prisma from "@/lib/prisma";
import { Prisma, UserStatus } from "@prisma/client";

export type TeacherWithUserAndSchool = Prisma.TeacherProfileGetPayload<{
  include: {
    user: {
      select: {
        firstName: true,
        lastName: true,
        email: true,
        phone: true,
        status: true,
        avatar: true,
      }
    };
    school: {
      select: {
        name: true,
        id: true
      }
    };
    courseAssignments: {
      select: {
        role: true,
        course: {
          select: {
            title: true,
          }
        }
      }
    }
  };
}>;

export async function getTeachers({
  sessionId,
  page,
  search,
  sortby,
  status = "APPROVED",
}: {
  sessionId: string;
  page: number;
  search?: string;
  sortby?: string;
  status?: UserStatus;
}): Promise<{
  teachers: TeacherWithUserAndSchool[]; // Corrected type here
  total: number;
}> {
  const whereClause: Prisma.TeacherProfileWhereInput = {};
  const orderByClause: Prisma.TeacherProfileOrderByWithRelationInput[] = [];

  whereClause.user = { status };
  whereClause.sessionId = sessionId;

  if (search) {
    whereClause.OR = [
      { user: { firstName: { contains: search } } },
      { user: { lastName: { contains: search } } },
      { user: { email: { contains: search } } },
      { school: { name: { contains: search } } },
    ];
  }

  if (sortby) {
    switch (sortby) {
      case "name":
        orderByClause.push({ user: { firstName: "asc" } });
        break;
      case "email":
        orderByClause.push({ user: { email: "asc" } });
        break;
      case "status":
        orderByClause.push({ user: { status: "asc" } });
        break;
      case "school":
        orderByClause.push({ school: { name: "asc" } });
        break;
      default:
        orderByClause.push({ user: { firstName: "asc" } });
        break;
    }
  } else {
    orderByClause.push({ user: { firstName: "asc" } });
  }

  const queryOptions: Prisma.TeacherProfileFindManyArgs = {
    include: {
      user: {
        select: {
          firstName: true,
          lastName: true,
          email: true,
          phone: true,
          status: true,
          avatar: true,
        }
      },
      school: {
        select: {
          name: true,
          id: true
        }
      },
      courseAssignments: {
        select: {
          role: true,
          course: { select: { title: true } }
        }
      }
    },
    where: whereClause,
    orderBy: orderByClause.length > 0 ? orderByClause : [{ user: { firstName: "asc" } }],
    skip: (page - 1) * appConfig.ITEMS_PER_PAGE,
    take: appConfig.ITEMS_PER_PAGE,
  };

  try {
    const teachers = await prisma.teacherProfile.findMany(queryOptions) as TeacherWithUserAndSchool[];

    const total = await prisma.teacherProfile.count({
      where: whereClause,
    });

    return { teachers, total };
  } catch (error) {
    console.error("Error fetching teachers:", error);
    throw new Error("Failed to fetch teachers.");
  }
}

export async function getTeacher(teacherId: string) {
  try {
    const teacher = await prisma.teacherProfile.findUnique({
      where: { id: teacherId },
      include: {
        user: true,
        school: true,
      },
    });

    return teacher;
  } catch (error) {
    console.error("Error fetching teacher:", error);
    throw new Error("Failed to fetch teacher.");
  }
}

export async function getTeacherOptions(sessionId: string) {
  try {
    const teachers = await prisma.teacherProfile.findMany({
      where: { sessionId },
      select: {
        id: true,
        user: {
          select: {
            firstName: true,
            lastName: true,
            email: true,
          }
        }
      },
    })
    
    return teachers.map(teacher => ({ id: teacher.id, name: `${teacher.user.firstName} ${teacher.user.lastName}`, email: teacher.user.email }))
  } catch (error) {
    console.log(error)
    return []
  }
}
