import { Button } from "@/components/ui/button";
import PageWrapper from "../_components/layout/PageWrapper";
import { Suspense } from "react";
import Loading from "../_components/Loading";
import Pagination from "../_components/table/Pagination";
import { DataTable } from "../_components/table/data-table";
import { getSchools } from "@/lib/server/action/schools";
import { columns } from "./column";
import { CustomSheet } from "@/components/shared/CustomSheet";
import SchoolForm from "@/components/form/SchoolForm";

const breadcrumbItems = [
  { label: "Home", href: "/dashboard" },
  { label: "Schools" },
];

async function SuspendedComponent({
  searchParams,
  params,
}: {
  searchParams: Promise<{ [key: string]: string | undefined }>;
  params: Promise<{ sessionId: string }>;
}) {
  const { page } = await searchParams;
  const currentPage = page ? +page : 1;
  const { sessionId } = await params;
  const { schools, total } = await getSchools({
    sessionId,
    page: currentPage,
  });

  return (
    <>
      <DataTable columns={columns} data={schools} />
      <Pagination page={currentPage} count={total} />
    </>
  );
}

export default function SchoolsPage({
  searchParams,
  params,
}: {
  searchParams: Promise<{ [key: string]: string | undefined }>;
  params: Promise<{ sessionId: string }>;
}) {
  const RenderButton = () => (
    <CustomSheet
      title="Create School"
      trigger={<Button size="sm">Add School</Button>}
    >
      <SchoolForm />
    </CustomSheet>
  );

  return (
    <PageWrapper
      pgTitle="Manage School"
      breadcrumbItems={breadcrumbItems}
      headerButton={<RenderButton />}
    >
      <Suspense fallback={<Loading />}>
        <SuspendedComponent searchParams={searchParams} params={params} />
      </Suspense>
    </PageWrapper>
  );
}
