import { PublishStatus } from "@prisma/client";
import { z } from "zod";

export const courseSchema = z.object({
  title: z.string().min(1, { message: "Please provide the course title" }),
  description: z.string().min(1, { message: "Please provide the course description" }),
  gradeId: z.string().min(1, { message: "Please select the grade" }),
  teacherId: z.string().min(1, { message: "Please select the teacher" }),
});

export type TCourse = z.infer<typeof courseSchema>;
export type TCourseForm = TCourse;
export type TUpdateCourseForm = TCourse & {
  isActive?: boolean
  status?: PublishStatus
  thumbnail?: string
};