import { Suspense } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Plus } from "lucide-react";
import CourseFilter from "./_components/filters/course-filter";
import CourseStatusCard from "./_components/cards/course/status-card";
import LoadMore from "./_components/LoadMore";
import Link from "next/link";
import { getCourses, getCourseStatusData } from "@/lib/server/action/courses";
import { getGradeOptions } from "@/lib/server/action/grades";
import { PublishStatus } from "@prisma/client";
import { appConfig } from "@/config/app";
import NoMore from "./_components/shared/no-more";
import Loading from "../_components/Loading";
import CourseCard from "./_components/cards/course/course-card";

async function CourseList({
  searchParams,
  params,
}: {
  searchParams: Promise<{ [key: string]: string | undefined }>;
  params: Promise<{ sessionId: string }>;
}) {
  const { sessionId } = await params;
  const { page, search, grade, sortby, status } = await searchParams;
  const currentPage = page ? +page : 1;
  const [courseData, gradeOptions, courseStatusData] = await Promise.all([
    getCourses({
      sessionId,
      page: currentPage,
      search,
      grade,
      sortby,
      status: status as PublishStatus,
    }),
    getGradeOptions(sessionId),
    getCourseStatusData(sessionId),
  ]);

  const { courses, total } = courseData;

  return (
    <div className="space-y-6 px-6 pb-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">All Courses</h1>
          <p className="text-gray-600 mt-1">
            Manage and organize your course content
          </p>
        </div>
        <Button size="sm" asChild>
          <Link href={`/dashboard/${sessionId}/courses/new`}>
            <Plus className="w-4 h-4" />
            Add Course
          </Link>
        </Button>
      </div>

      {/* Search and Filters */}
      <CourseFilter grades={gradeOptions} />

      {/* Stats */}
      <CourseStatusCard
        total={courseStatusData.total}
        draft={courseStatusData.draft}
        published={courseStatusData.published}
        students={courseStatusData.students}
      />

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {courses.map((course) => {
          const primaryTeacher = course.teacherAssignments.find(
            (ta) => ta.role === "PRIMARY"
          );
          const primaryTeacherName = primaryTeacher
            ? primaryTeacher.teacher.user.firstName +
              " " +
              primaryTeacher.teacher.user.lastName
            : "N/A";
          return (
            <CourseCard
              key={course.id}
              course={course}
              primaryTeacherName={primaryTeacherName}
              sessionId={sessionId}
            />
          );
        })}
      </div>

      {total > appConfig.ITEMS_PER_PAGE ? <LoadMore /> : null}
      {total === 0 && (
        <NoMore
          text="No courses found"
          link={`/dashboard/${sessionId}/courses`}
        />
      )}
    </div>
  );
}

export default async function CoursesPage({
  searchParams,
  params,
}: {
  searchParams: Promise<{ [key: string]: string | undefined }>;
  params: Promise<{ sessionId: string }>;
}) {
  return (
    <>
      <Suspense fallback={<Loading />}>
        <CourseList searchParams={searchParams} params={params} />
      </Suspense>
    </>
  );
}
