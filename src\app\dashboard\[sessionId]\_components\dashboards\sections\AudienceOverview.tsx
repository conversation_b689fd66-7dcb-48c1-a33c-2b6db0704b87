import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface AudienceOverviewProps {
  className?: string;
}

export function AudienceOverview({ className }: AudienceOverviewProps) {
  return (
    <Card className={className}>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Audience Overview</CardTitle>
        <Select defaultValue="year">
          <SelectTrigger className="w-[120px]">
            <SelectValue placeholder="Period" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="year">This Year</SelectItem>
            <SelectItem value="month">This Month</SelectItem>
            <SelectItem value="week">This Week</SelectItem>
          </SelectContent>
        </Select>
      </CardHeader>
      <CardContent>
        <div className="h-[300px] w-full">
          {/* This would be a chart component in a real app */}
          <div className="flex h-full items-center justify-center rounded-md border border-dashed">
            <p className="text-sm text-muted-foreground">
              Chart visualization would go here
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
