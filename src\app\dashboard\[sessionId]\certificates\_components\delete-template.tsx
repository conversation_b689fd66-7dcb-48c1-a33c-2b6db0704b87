"use client";

import CustomAlertDialog from "@/components/shared/CustomAlertDialog";
import { Button } from "@/components/ui/button";
import { deleteTemplate } from "@/lib/server/action/certificates/templates";
import { Trash2 } from "lucide-react";
import { toast } from "sonner";

export default function DeleteTemplate({ templateId }: { templateId: string }) {
  return (
    <CustomAlertDialog
      trigger={
        <Button size="icon" variant="destructive" className="cursor-pointer">
          <Trash2 />
        </Button>
      }
      asChild
      title="Delete Template"
      description="Are you sure you want to delete this template?"
      onConfirm={async () => {
        const res = await deleteTemplate(templateId);
        if (res.success) {
          toast.success("Template deleted");
        } else {
          toast.error("Failed to delete template");
        }
      }}
    />
  );
}
