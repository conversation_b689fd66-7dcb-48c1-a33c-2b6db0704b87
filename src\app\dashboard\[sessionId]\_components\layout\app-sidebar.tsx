"use client";

import type React from "react";

import { useMemo } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  BarChart2,
  BookOpen,
  ChevronRight,
  Cog,
  GraduationCap,
  LogOut,
  MessageSquare,
  Notebook,
  School,
  Settings2,
  User,
  Users,
} from "lucide-react";

import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  SidebarRail,
} from "@/components/ui/sidebar";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { Separator } from "@/components/ui/separator";
import Logo from "@/components/shared/logo";
import { logout } from "@/lib/server/action/users/user.action";

interface SidebarSubItem {
  title: string;
  url: string;
}

interface SidebarLink {
  title: string;
  url: string;
  icon: React.ElementType;
  items?: SidebarSubItem[];
}

// Define sidebar links as a constant outside the component to prevent re-creation on each render
const SIDEBAR_LINKS: SidebarLink[] = [
  {
    title: "Dashboard",
    url: "/",
    icon: BarChart2,
    items: [
      {
        title: "Overview",
        url: "/",
      },
    ],
  },
  {
    title: "Schools",
    url: "/schools",
    icon: School,
  },
  {
    title: "Admins",
    url: "/admin",
    icon: Users,
    items: [
      {
        title: "Admins",
        url: "/admins",
      },
      {
        title: "New Admin",
        url: "/admins/new",
      },
    ],
  },
  {
    title: "Students",
    url: "/students",
    icon: Users,
    items: [
      {
        title: "Students",
        url: "/students",
      },
      {
        title: "New Student",
        url: "/students/new",
      },
      {
        title: "Pending Request",
        url: "/students/pending",
      },
      {
        title: "Results",
        url: "/students/results",
      },
    ],
  },
  {
    title: "Teachers",
    url: "/teachers",
    icon: Users,
    items: [
      {
        title: "Teachers",
        url: "/teachers",
      },
      {
        title: "New Teacher",
        url: "/teachers/new",
      },
      {
        title: "Pending Request",
        url: "/teachers/pending",
      },
    ],
  },
  {
    title: "Grade Levels",
    url: "/grades",
    icon: GraduationCap,
  },
  {
    title: "Courses",
    url: "/courses",
    icon: BookOpen,
  },
  {
    title: "Session Management",
    url: "/sessions",
    icon: Cog,
  },
  {
    title: "Certificate Templates",
    url: "/certificates",
    icon: Notebook,
  },
  {
    title: "Codes",
    url: "/codes",
    icon: MessageSquare,
  },
  {
    title: "Profile",
    url: "/profile",
    icon: User,
  },
  {
    title: "Settings",
    url: "/settings",
    icon: Settings2,
  },
];

function AppSidebar({ activeSessionId }: { activeSessionId: string }) {
  const pathname = usePathname();

  // Memoize the active link and open collapsibles to prevent unnecessary re-renders
  const { activeLinks, openCollapsibles } = useMemo(() => {
    const active = new Set<string>();
    const open = new Set<string>();

    // Check which links should be active and which collapsibles should be open
    SIDEBAR_LINKS.forEach((link) => {
      // Check if the current path matches the main link
      if (pathname === `/dashboard/${activeSessionId}${link.url}`) {
        active.add(link.url);
      }

      // Check if any sub-item matches the current path
      if (link.items) {
        const hasActiveChild = link.items.some((item) => {
          const isActive =
            pathname === `/dashboard/${activeSessionId}${item.url}`;
          if (isActive) {
            active.add(item.url);
            // If a child is active, the parent collapsible should be open
            open.add(link.title);
          }
          return isActive;
        });

        // If the main path starts with the link URL (for partial matches)
        if (
          !hasActiveChild &&
          pathname.startsWith(`/dashboard/${activeSessionId}${link.url}`) &&
          link.url !== "/"
        ) {
          open.add(link.title);
        }
      }
    });

    return { activeLinks: active, openCollapsibles: open };
  }, [pathname, activeSessionId]);

  // Split links into those with dropdown menus and those without
  const linksWithDropdowns = SIDEBAR_LINKS.filter(
    (link) => link.items && link.items.length > 0
  );
  const linksWithoutDropdowns = SIDEBAR_LINKS.filter(
    (link) => !link.items || link.items.length === 0
  );

  return (
    <Sidebar collapsible="icon" className="overflow-hidden">
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton size="lg" asChild className="gap-0">
              <Logo />
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel>Platform</SidebarGroupLabel>
          <SidebarMenu>
            {/* Render links with dropdowns */}
            {linksWithDropdowns.map((item) => (
              <Collapsible
                key={item.title}
                asChild
                defaultOpen={openCollapsibles.has(item.title)}
                className="group/collapsible"
              >
                <SidebarMenuItem>
                  <CollapsibleTrigger asChild>
                    <SidebarMenuButton
                      tooltip={item.title}
                      className="h-10 px-2.5"
                      isActive={activeLinks.has(item.url)}
                    >
                      <item.icon className="h-4 w-4" />
                      <span>{item.title}</span>
                      <ChevronRight className="ml-auto h-4 w-4 transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                    </SidebarMenuButton>
                  </CollapsibleTrigger>
                  <CollapsibleContent>
                    <SidebarMenuSub>
                      {item.items?.map((subItem) => (
                        <SidebarMenuSubItem key={subItem.title}>
                          <SidebarMenuSubButton
                            asChild
                            className="h-8"
                            isActive={activeLinks.has(subItem.url)}
                          >
                            <Link
                              href={`/dashboard/${activeSessionId}${subItem.url}`}
                            >
                              <span>{subItem.title}</span>
                            </Link>
                          </SidebarMenuSubButton>
                        </SidebarMenuSubItem>
                      ))}
                    </SidebarMenuSub>
                  </CollapsibleContent>
                </SidebarMenuItem>
              </Collapsible>
            ))}

            {/* Render links without dropdowns */}
            {linksWithoutDropdowns.map((item) => (
              <SidebarMenuItem key={item.title}>
                <SidebarMenuButton
                  tooltip={item.title}
                  className="h-10 px-2.5"
                  asChild
                  isActive={activeLinks.has(item.url)}
                >
                  <Link href={`/dashboard/${activeSessionId}${item.url}`}>
                    <item.icon className="h-4 w-4" />
                    <span>{item.title}</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
            ))}

            <Separator className="my-4" />

            {/* Sign out button */}
            <SidebarMenuItem>
              <SidebarMenuButton
                tooltip="Sign out"
                className="h-10 px-2.5 text-red-500 hover:bg-red-500/10 hover:text-red-600"
                asChild
              >
                <button
                  onClick={async () => {
                    await logout();
                  }}
                >
                  <LogOut className="h-4 w-4" />
                  <span>Sign out</span>
                </button>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarGroup>
      </SidebarContent>
      <SidebarRail />
    </Sidebar>
  );
}

export default AppSidebar;
