interface PartnersSectionProps {
  data: {
    title: string;
    logos: Array<{
      name: string;
      icon: string;
    }>;
  };
}

export default function PartnersSection({ data }: PartnersSectionProps) {
  return (
    <section className="w-full py-12 bg-white">
      <div className="container mx-auto px-4">
        <h2 className="text-center text-2xl font-semibold text-gray-800 mb-8">
          {data.title}
        </h2>
        <div className="flex flex-wrap justify-center items-center gap-8 md:gap-12">
          {data.logos.map((logo, index) => (
            <div
              key={index}
              className="flex items-center justify-center w-16 h-16 bg-gray-100 rounded-full hover:bg-gray-200 transition-colors"
            >
              <span className="text-2xl">{logo.icon}</span>
            </div>
          ))}
        </div>
        <div className="flex justify-center mt-6 space-x-2">
          <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
          <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
          <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
        </div>
      </div>
    </section>
  );
}
