import { Activity, Bar<PERSON><PERSON>, Clock } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { cn } from "@/lib/utils";

interface StatsCardProps {
  title: string;
  value: string;
  icon: "activity" | "clock" | "bar-chart";
  change: number;
  changeText: string;
  changeType: "increase" | "decrease";
  increaseBad?: boolean;
}

export function StatsCard({
  title,
  value,
  icon,
  change,
  changeText,
  changeType,
  increaseBad = false,
}: StatsCardProps) {
  const isPositive = changeType === "increase";
  const isNegative = !isPositive;
  const isGood = (isPositive && !increaseBad) || (isNegative && increaseBad);
  const isBad = (isPositive && increaseBad) || (isNegative && !increaseBad);

  const iconMap = {
    activity: <Activity className="h-5 w-5 text-blue-500" />,
    clock: <Clock className="h-5 w-5 text-blue-500" />,
    "bar-chart": <BarChart className="h-5 w-5 text-blue-500" />,
  };

  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-muted-foreground">{title}</p>
            <h2 className="text-3xl font-bold">{value}</h2>
          </div>
          <div className="rounded-full bg-blue-50 p-2 dark:bg-blue-950">
            {iconMap[icon]}
          </div>
        </div>
        <div className="mt-4">
          <p
            className={cn(
              "text-sm",
              isGood && "text-green-500",
              isBad && "text-red-500"
            )}
          >
            {change}% {changeText}
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
