'use server'

import prisma from "@/lib/prisma"
import { LessonAction } from "./lesson.schema"
import { revalidatePath } from "next/cache"
import { deleteFromBunny, deleteVideo } from "../../../bunny/bunny.action"

export async function createLesson (moduleId: string, data: LessonAction) {
  try {
    const lesson = await prisma.lesson.create({
      data: {
        ...data,
        module: { connect: { id: moduleId } }
      },
      select: { module: { select: { course: { select: { id: true } } } } }
    })

    const courseId = lesson.module.course.id
    
    revalidatePath(`/admin/courses/${courseId}/modules/${moduleId}`)

    return { success: true, message: 'Lesson created' }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to create lesson' }
  }
}

export async function updateLesson (lessonId: string, data: LessonAction) {
  try {
    const lesson = await prisma.lesson.update({
      where: { id: lessonId },
      data,
      select: { module: { select: { id: true, course: { select: { id: true } } } } }
    })

    const courseId = lesson.module.course.id
    
    revalidatePath(`/admin/courses/${courseId}/modules/${lesson.module.id}`)
    
    return { success: true, message: 'Lesson updated' }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to update lesson' }
  }
}

export async function deleteLesson (lessonId: string) {
  try {
    const lesson = await prisma.lesson.findUnique({
      where: { id: lessonId },
      select: { fileId: true, module: { select: { id: true, course: { select: { id: true } } } } }
    })
    
    if (!lesson) {
      return { success: false, error: 'Lesson not found' }
    }
    
    if (lesson.fileId) {
      await deleteFromBunny(lesson.fileId)
    }

    await prisma.lesson.delete({
      where: { id: lessonId },
    })

    const courseId = lesson.module.course.id
    
    revalidatePath(`/admin/courses/${courseId}/modules/${lesson.module.id}`)
    
    return { success: true, message: 'Lesson deleted' }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to delete lesson' }
  }
}

export async function deleteLessonFile (lessonId: string) {
  try {
    const lesson = await prisma.lesson.findUnique({
      where: { id: lessonId },
      select: { type: true, fileId: true, module: { select: { id: true, course: { select: { id: true } } } } }
    })

    if (!lesson) {
      return { success: false, error: 'Lesson not found' }
    }

    if (lesson.fileId) {
      if (lesson.type === 'VIDEO') {
        await deleteVideo(lesson.fileId)
      } else {
        await deleteFromBunny(lesson.fileId)
      }
    }

    await prisma.lesson.update({
      where: { id: lessonId },
      data: { fileId: "", fileUrl: "", mimeType: null, originalName: null, fileSize: null },
    })

    const courseId = lesson.module.course.id
    
    revalidatePath(`/admin/courses/${courseId}/modules/${lesson.module.id}`)
    
    return { success: true, message: 'Lesson file deleted' }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to delete lesson file' }
  }
}