'use server'

import prisma from "@/lib/prisma"
import { TLessonForm } from "./lesson.schema"
import { revalidatePath } from "next/cache"

export async function createLesson (moduleId: string, data: TLessonForm & { duration?: string }) {
  try {
    const lesson = await prisma.lesson.create({
      data: {
        ...data,
        module: { connect: { id: moduleId } }
      },
      select: { module: { select: { course: { select: { id: true } } } } }
    })

    const courseId = lesson.module.course.id
    
    revalidatePath(`/admin/courses/${courseId}/modules/${moduleId}`)

    return { success: true, message: 'Lesson created' }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to create lesson' }
  }
}

export async function updateLesson (lessonId: string, data: TLessonForm & { duration?: string }) {
  try {
    const lesson = await prisma.lesson.update({
      where: { id: lessonId },
      data,
      select: { module: { select: { id: true, course: { select: { id: true } } } } }
    })

    const courseId = lesson.module.course.id
    
    revalidatePath(`/admin/courses/${courseId}/modules/${lesson.module.id}`)
    
    return { success: true, message: 'Lesson updated' }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to update lesson' }
  }
}

export async function deleteLesson (lessonId: string) {
  try {
    const lesson = await prisma.lesson.delete({
      where: { id: lessonId },
      select: { module: { select: { id: true, course: { select: { id: true } } } } }
    })

    const courseId = lesson.module.course.id
    
    revalidatePath(`/admin/courses/${courseId}/modules/${lesson.module.id}`)
    
    return { success: true, message: 'Lesson deleted' }
  } catch (error) {
    console.log(error)
    return { success: false, error: 'Failed to delete lesson' }
  }
}